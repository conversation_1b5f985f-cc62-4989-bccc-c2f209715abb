import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import type {ProductType} from '../../modules/catalog/types/products';

export interface CartItemType extends ProductType {
  quantity: number;
  selectedSize?: string;
  selectedColor?: string;
  cartPrice: number;
}

const getProductPrice = (product: ProductType): number => {
  const firstItem = product.items?.[0];
  const firstPrice = firstItem?.prices?.[0];
  return firstPrice?.promotionalPrice || firstPrice?.realPrice || 0;
};

const createCartItem = (
  product: ProductType,
  selectedSize?: string,
  selectedColor?: string,
): CartItemType => {
  return {
    ...product,
    quantity: 1,
    selectedSize,
    selectedColor,
    cartPrice: getProductPrice(product),
  };
};

const initialState = {
  list: [] as CartItemType[],
  total: 0,
  discount: 2.88,
  delivery: 2,
};

type StateType = typeof initialState;

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (
      state: StateType = initialState,
      action: PayloadAction<
        ProductType & {selectedSize?: string; selectedColor?: string}
      >,
    ) => {
      const {selectedSize, selectedColor, ...product} = action.payload;
      const inCart = state.list.find((item) => item.id === product.id);

      if (inCart) {
        state.list.map((item: CartItemType) => {
          if (item.id === product.id) {
            item.quantity += 1;
          }
          return item;
        }, state);
        state.total += inCart.cartPrice;
      } else {
        const cartItem = createCartItem(product, selectedSize, selectedColor);
        state.list.push(cartItem);
        state.total += cartItem.cartPrice;
      }
    },
    removeFromCart: (state, action: PayloadAction<ProductType>) => {
      const inCart = state.list.find((item) => item.id === action.payload.id);

      if (inCart) {
        state.list.map((item) => {
          if (item.id === action.payload.id && item.quantity > 1) {
            item.quantity -= 1;
          } else if (item.id === action.payload.id && item.quantity === 1) {
            state.list.splice(state.list.indexOf(item), 1);
          }
          return item;
        }, state);
        state.total -= inCart.cartPrice;
      }
    },

    fullRemoveFromCart: (state, action: PayloadAction<ProductType>) => {
      const inCart = state.list.find((item) => item.id === action.payload.id);

      if (inCart) {
        state.total -= inCart.cartPrice * inCart.quantity;
        state.list = state.list.filter((item) => item.id !== action.payload.id);
      }
    },

    resetCart: (state) => {
      state.list = [];
      state.total = 0;
    },
  },
});

export const {addToCart, removeFromCart, resetCart, fullRemoveFromCart} =
  cartSlice.actions;

export default cartSlice.reducer;
