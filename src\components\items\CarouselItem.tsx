import React from 'react';
import {View, ImageBackground} from 'react-native';
import type {PropsWithChildren} from 'react';

import {text} from '../../text';
import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import type {CarouselType} from '../../types';
import type {ProductType} from '../../modules/catalog/types/products';

type Props = PropsWithChildren<{
  item: CarouselType;
  array: CarouselType[];
  index: number;
  sale: ProductType[];
}>;

import ShopNow from '../buttons/ShopNow';

const CarouselItem: React.FC<Props> = ({
  item,
  array,
  index,
  sale,
}): JSX.Element => {
  const navigation = useAppNavigation();

  const dotStyle = {
    width: 10,
    height: 10,
    marginHorizontal: 3,
    borderRadius: 5,
  };

  return (
    <ImageBackground
      source={{uri: item.image}}
      style={{
        width: theme.sizes.width,
        height: 'auto',
        paddingHorizontal: 20,
        marginBottom: 20,
        justifyContent: 'flex-end',
        aspectRatio: 0.75,
      }}
      resizeMode='cover'
    >
      <View style={{marginBottom: 30}}>
        <text.H1 numberOfLines={1}>{item.title_line_1}</text.H1>
        <text.H1 numberOfLines={1} style={{marginBottom: 30}}>
          {item.title_line_2}
        </text.H1>
        <ShopNow
          text={item.button_text}
          containerStyle={{marginBottom: 50}}
          onPress={() => {
            // TODO: Update Shop screen to handle catalog ProductType
            navigation.navigate('Shop', {title: 'Sale', products: []});
          }}
        />
        {array.length > 1 && (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              marginBottom: 20,
            }}
          >
            {array.map((_, current) => {
              const backgroundColor =
                current === index ? theme.colors.mainColor : theme.colors.white;
              return (
                <View
                  key={current}
                  style={{
                    ...dotStyle,
                    backgroundColor: backgroundColor,
                  }}
                />
              );
            })}
          </View>
        )}
      </View>
    </ImageBackground>
  );
};

export default CarouselItem;
