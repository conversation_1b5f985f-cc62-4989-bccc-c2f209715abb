import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import useCouponCodeVerification from "../hooks/use-coupon-code-verification";

export default function CouponCodeInput() {
  const t = useTranslations("checkoutPage.orderSummary.couponCode");
  const {
    error,
    promotionWarning,
    isLoading,
    verifyCouponCode,
    couponCode,
    setCouponCode,
    validatedCouponCode,
  } = useCouponCodeVerification();

  return (
    <div className="flex flex-col space-y-4 items-center">
      <p className="text-gray">
        <Text textStyle="TS6">{t("message")}</Text>
      </p>
      <div className="w-full flex flex-col space-y-2">
        <Text textStyle="TS6" className="text-danger">
          {error}
        </Text>

        {promotionWarning && (
          <Text textStyle="TS6" className="text-secondary">
            {promotionWarning}
          </Text>
        )}

        <div className=" flex gap-2 w-full">
          <Input
            id="couponCode"
            className={cn(
              "bg-white rounded-xl w-full h-[50px]",
              TextStyle["TS7"]
            )}
            placeholder={t("inputPlaceholder")}
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
          />

          <Button
            variant="secondary"
            disabled={isLoading}
            onClick={verifyCouponCode}
            className={cn(
              "rounded-xl bg-primary text-white hover:bg-primary-700 border border-primary w-full h-[50px]",
              {
                "scale-95 opacity-80": isLoading,
              }
            )}
          >
            <Text textStyle="TS6">{t("buttonLabel")}</Text>
          </Button>
        </div>
        {error === "" &&
          promotionWarning === "" &&
          validatedCouponCode !== null && (
            <Text textStyle="TS6" className="text-primary">
              {t("validatedCoupon")}
            </Text>
          )}
      </div>
    </div>
  );
}
