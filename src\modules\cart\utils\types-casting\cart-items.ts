import { ProductInResponse, ProductItemType } from "../../types/products";

export default function castToCartItem(
  productInResponse: ProductInResponse
): ProductItemType {
  return {
    slug: productInResponse.slug,
    productId: productInResponse.productItem.productId,
    productItemCartId: productInResponse.id,
    id: productInResponse.productItem.id,
    name: productInResponse.productItem.name,
    image: `${process.env.BACKEND_ADDRESS}${productInResponse.productItem.image}`,
    cartQuantity: productInResponse.quantity,
    variations: [],
    prices: productInResponse.productItem.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: productInResponse.productItem.inStock,
  };
}
