import { ProductItemType } from "@/modules/cart/types/products";
import { CouponCodeType } from "../types";

export interface CouponPromotionValidationResult {
  canApplyToAnyProduct: boolean;
  productsWithPromotionsNotCovered: ProductItemType[];
  allProductsHavePromotions: boolean;
}

export function validateCouponOnPromotions(
  couponCode: CouponCodeType,
  cartItems: ProductItemType[]
): CouponPromotionValidationResult {
  // If coupon allows on promotions it can be applied to all products
  if (couponCode.allowOnPromotions) {
    return {
      canApplyToAnyProduct: true,
      productsWithPromotionsNotCovered: [],
      allProductsHavePromotions: false,
    };
  }

  const productsWithPromotions = cartItems.filter(
    (item) => item.prices[0].promotionalPrice !== item.prices[0].realPrice
  );

  const productsWithoutPromotions = cartItems.filter(
    (item) => item.prices[0].promotionalPrice === item.prices[0].realPrice
  );

  const allProductsHavePromotions =
    productsWithPromotions.length === cartItems.length;
  const canApplyToAnyProduct = productsWithoutPromotions.length > 0;

  return {
    canApplyToAnyProduct,
    productsWithPromotionsNotCovered: productsWithPromotions,
    allProductsHavePromotions,
  };
}

export function getProductNamesNotCoveredByCoupon(
  productsWithPromotions: ProductItemType[]
): string {
  return productsWithPromotions.map((product) => product.name).join(", ");
}
