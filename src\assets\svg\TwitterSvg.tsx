import * as React from 'react';
import Svg, {<PERSON><PERSON>, <PERSON>, <PERSON>, De<PERSON>, ClipPath} from 'react-native-svg';

const TwitterSvg: React.FC = (): JSX.Element => (
  <Svg width={105} height={40} fill='none'>
    <Rect x={0.5} y={0.5} width={104} height={39} rx={4.5} fill='#fff' />
    <G clipPath='url(#a)'>
      <Path
        d='M60.637 14.962c-.25.111-.51.206-.773.282a3.42 3.42 0 0 0 .696-1.225.258.258 0 0 0-.377-.3 6.091 6.091 0 0 1-1.8.711 3.452 3.452 0 0 0-2.405-.98 3.439 3.439 0 0 0-3.406 3.879 8.793 8.793 0 0 1-6.036-3.202.258.258 0 0 0-.423.033 3.433 3.433 0 0 0-.465 1.727c0 .828.295 1.613.817 2.226a2.91 2.91 0 0 1-.46-.205.258.258 0 0 0-.383.22v.046c0 1.235.665 2.347 1.681 2.953a2.979 2.979 0 0 1-.261-.038.258.258 0 0 0-.294.333 3.433 3.433 0 0 0 2.516 2.302 6.076 6.076 0 0 1-3.248.927 6.2 6.2 0 0 1-.728-.043.258.258 0 0 0-.17.474 9.262 9.262 0 0 0 5.01 1.468c3.497 0 5.684-1.65 6.904-3.033 1.52-1.724 2.392-4.008 2.392-6.264 0-.094-.001-.19-.004-.284a6.678 6.678 0 0 0 1.536-1.628.258.258 0 0 0-.319-.379Z'
        fill='#142535'
      />
    </G>
    <Rect x={0.5} y={0.5} width={104} height={39} rx={4.5} stroke='#DBE9F5' />
    <Defs>
      <ClipPath id='a'>
        <Path fill='#fff' transform='translate(45 13)' d='M0 0h16v14H0z' />
      </ClipPath>
    </Defs>
  </Svg>
);

export default TwitterSvg;
