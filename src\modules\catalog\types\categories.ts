export interface CategoryType {
  name: string;
  image: string | null;
  bannerImage: string | null;
  id: string;
  slug: string;
  description?: string | null;
  subCategories: CategoryType[];
  numberOfProducts?: number;
}
export interface CategoryInResponseType {
  id: string;
  slug: string;
  name: string;
  description?: string | null;
  menuImage: string | null;
  bannerImage: string | null;
  createdAt?: string;
  subCategories: CategoryInResponseType[];
  numberOfProducts?: number;
}

//used in filter page
export interface CategorySelectionType {
  name: string;
  image: string | null;
  id: string;
  slug: string;
  selected: boolean;
  subCategories: CategorySelectionType[];
}
