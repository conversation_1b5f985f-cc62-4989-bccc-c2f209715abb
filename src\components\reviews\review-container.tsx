import Text from "@/styles/text-styles";
import { Star } from "lucide-react";

export interface Props {
  name: string;
  rate: number;
  review: string;
}

export default function ReviewContainer(props: Props) {
  return (
    <div className="max-h-[300px] bg-primary M:px-8 M:py-7 px-5 py-4 M:max-w-[370px] S:max-w-[270px] max-w-[210px] snap-center rounded-[20px] border-[1px] border-primary flex flex-col text-white">
      {/* Stars Rating - Centered */}
      <div className="flex justify-center space-x-1 mb-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            className={`w-6 h-6 ${
              index < props.rate
                ? "fill-blue text-blue"
                : "fill-white/30 text-white/30"
            }`}
          />
        ))}
      </div>

      {/* Review Text - Centered */}
      <Text
        textStyle="TS7"
        className="flex-1 text-white text-center mb-8 leading-relaxed"
      >
        {props.review}
      </Text>

      {/* User Info - Centered */}
      <div className="text-center">
        <Text textStyle="TS6" className="text-blue mb-1 font-bold">
          {props.name}
        </Text>
      </div>
    </div>
  );
}
