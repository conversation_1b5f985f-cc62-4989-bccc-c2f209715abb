import { ProductItemType } from "@/modules/cart/types/products";
import { CouponCodeType } from "../types";
import { getDiscountedPrice } from "./coupon-discount-amount";

export interface ProductCouponDiscount {
  productName: string;
  originalPrice: number;
  discountedPrice: number;
  discountAmount: number;
  quantity: number;
  totalDiscountAmount: number;
  couponApplied: boolean;
}

export function calculateDetailedCouponBreakdown(
  couponCode: CouponCodeType | null,
  cartItems: ProductItemType[]
): ProductCouponDiscount[] {
  if (!couponCode || !couponCode.discount) {
    return cartItems.map((item) => ({
      productName: item.name,
      originalPrice: item.prices[0].promotionalPrice,
      discountedPrice: item.prices[0].promotionalPrice,
      discountAmount: 0,
      quantity: item.cartQuantity,
      totalDiscountAmount: 0,
      couponApplied: false,
    }));
  }

  return cartItems.map((item) => {
    const itemPrice = item.prices[0].promotionalPrice;
    const itemTotalPrice = itemPrice * item.cartQuantity;

    const itemHasPromotion =
      item.prices[0].promotionalPrice !== item.prices[0].realPrice;
    const canApplyCoupon = itemHasPromotion
      ? couponCode.allowOnPromotions
      : true;

    if (canApplyCoupon) {
      const discountedTotalPrice = getDiscountedPrice(
        couponCode,
        itemTotalPrice
      );
      const totalDiscountAmount = itemTotalPrice - discountedTotalPrice;
      const discountPerUnit = totalDiscountAmount / item.cartQuantity;
      const discountedPricePerUnit = itemPrice - discountPerUnit;

      return {
        productName: item.name,
        originalPrice: itemPrice,
        discountedPrice: discountedPricePerUnit,
        discountAmount: discountPerUnit,
        quantity: item.cartQuantity,
        totalDiscountAmount,
        couponApplied: true,
      };
    } else {
      return {
        productName: item.name,
        originalPrice: itemPrice,
        discountedPrice: itemPrice,
        discountAmount: 0,
        quantity: item.cartQuantity,
        totalDiscountAmount: 0,
        couponApplied: false,
      };
    }
  });
}

export function getTotalDiscountFromBreakdown(
  breakdown: ProductCouponDiscount[]
): number {
  return breakdown.reduce((total, item) => total + item.totalDiscountAmount, 0);
}
