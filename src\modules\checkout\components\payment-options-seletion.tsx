import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { useState } from "react";

export default function PaymentOptionsSelection() {
  const [paymentMethod, setPaymentMethod] = useState("cashOnDelivery");
  const t = useTranslations("checkoutPage.form.payment");

  return (
    <RadioGroup
      value={paymentMethod}
      onValueChange={(value) => setPaymentMethod(value)}
      className={cn("space-y-3")}
    >
      <Label
        className={cn(
          "border-primary p-4 rounded-[15px] flex items-center space-x-2",
          {
            border: paymentMethod === "cashOnDelivery",
          }
        )}
      >
        <RadioGroupItem
          value={paymentMethod}
          className="border border-black text-black"
        />
        <Text textStyle="TS7" className="font-bold text-black">
          {t("onDelivery")}
        </Text>
      </Label>

      {/*
        <label className={cn("flex space-x-2", {
            "space-x-reverse": locale === "ar-LY",
          })}>
          <RadioGroupItem
            value="card"
            className="accent-secondary w-[22px] h-[22px] rounded-full bg-transparent border border-secondary text-secondary"
          />
          <Text textStyle="TS5">{content.cardPayment}</Text>
        </label>
        */}
    </RadioGroup>
  );
}
