import {AxiosError} from 'axios';
import extractJWTokens from '../../auth/utils/jwt/extract-tokens';

import {refreshToken} from '../../auth/services/refresh-token';
import {CustomError} from '../../../lib/custom-error';

import {POST} from '../../../lib/http-methods';

export async function addWishedItemOnServerSide(item: {id: string}) {
  const {access} = await extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await POST('/favourites/products/register', header, {
      productItemId: item.id,
    });
    return {ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() => addWishedItemOnServerSide(item));
      if (!res) throw new CustomError('Unauthorized', 401);
    } else if (axiosError.response?.status == 404) {
      throw new CustomError('Product Not Found!', 404);
    } else throw new CustomError('Server Error!', 500);
  }
}
