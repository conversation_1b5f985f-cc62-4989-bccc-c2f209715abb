import React, {useEffect, useState} from 'react';
import {View, Text, Image, TouchableOpacity, Alert} from 'react-native';
import {theme} from '../../constants';
import {WishedProductItemType} from '../../modules/wishlist/types/products';
import {useWishlistStore} from '../../modules/wishlist/store/wishlist-store';
import {useCartStore} from '../../modules/cart/store/cart-store';
import {formatPrice} from '../../modules/catalog/utils/prices-transformation';
import useCurrency from '../../modules/catalog/hooks/use-currency';
import {svg} from '../../assets/svg';
import Button from '../buttons/Button';

interface Props {
  productItem: WishedProductItemType;
}

const WishlistProductContainer: React.FC<Props> = ({productItem}) => {
  const [productImage, setProductImage] = useState(
    '/not-found/product-image.webp',
  );

  const {removeProductItem} = useWishlistStore((store) => store.actions);
  const {addProductItem} = useCartStore((store) => store.actions);
  const {currency} = useCurrency();

  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  const handleRemoveFromWishlist = () => {
    Alert.alert(
      'Remove from Wishlist',
      'Are you sure you want to remove this item from your wishlist?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => removeProductItem(productItem.productItemId),
        },
      ],
    );
  };

  const handleAddToCart = () => {
    addProductItem({
      slug: productItem.slug,
      id: productItem.productItemId,
      productId: productItem.productId,
      cartQuantity: 1,
      name: productItem.name,
      prices: productItem.prices,
      image: productItem.image,
      variations: productItem.variations,
      inStock: productItem.inStock,
    });
  };

  return (
    <View
      style={{
        width: '100%',
        backgroundColor: theme.colors.white,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        padding: 16,
        marginBottom: 12,
      }}
    >
      <View style={{flexDirection: 'row', gap: 12}}>
        {/* Product Image */}
        <View style={{position: 'relative', flexShrink: 0}}>
          <Image
            source={{uri: productImage}}
            style={{
              width: 80,
              height: 80,
              borderRadius: 6,
              backgroundColor: theme.colors.imageBackground,
              borderWidth: 1,
              borderColor: '#E5E5E5',
            }}
            resizeMode="cover"
            onError={() => setProductImage('/not-found/product-image.webp')}
          />
          {promotionIsAvailable && (
            <View
              style={{
                position: 'absolute',
                top: -4,
                right: -4,
                backgroundColor: '#EF4444',
                width: 16,
                height: 16,
                borderRadius: 8,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Text
                style={{
                  color: theme.colors.white,
                  fontSize: 10,
                  fontWeight: 'bold',
                }}
              >
                %
              </Text>
            </View>
          )}
        </View>

        {/* Product Info */}
        <View style={{flex: 1, minWidth: 0}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: 8,
            }}
          >
            <Text
              style={{
                ...theme.fonts.DMSans_500Medium,
                fontSize: 14,
                lineHeight: 14 * 1.4,
                color: theme.colors.mainColor,
                flex: 1,
                paddingRight: 8,
              }}
              numberOfLines={2}
            >
              {productItem.name}
            </Text>
            <TouchableOpacity
              style={{
                padding: 4,
                flexShrink: 0,
              }}
              onPress={handleRemoveFromWishlist}
            >
              <svg.HeartSvg />
            </TouchableOpacity>
          </View>

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 12,
            }}
          >
            <View>
              {promotionIsAvailable && (
                <Text
                  style={{
                    ...theme.fonts.DMSans_400Regular,
                    fontSize: 12,
                    lineHeight: 12 * 1.3,
                    color: theme.colors.textColor,
                    textDecorationLine: 'line-through',
                  }}
                >
                  {formatPrice(productItem.prices[0].realPrice)} {currency}
                </Text>
              )}
              <Text
                style={{
                  ...theme.fonts.DMSans_700Bold,
                  fontSize: 16,
                  lineHeight: 16 * 1.3,
                  color: theme.colors.mainColor,
                }}
              >
                {formatPrice(productItem.prices[0].promotionalPrice)} {currency}
              </Text>
            </View>
            <View
              style={{
                backgroundColor: productItem.inStock ? '#10B981' : '#EF4444',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 4,
              }}
            >
              <Text
                style={{
                  ...theme.fonts.DMSans_400Regular,
                  fontSize: 12,
                  color: theme.colors.white,
                }}
              >
                {productItem.inStock ? 'In Stock' : 'Out of Stock'}
              </Text>
            </View>
          </View>

          <Button
            title="Add to Cart"
            onPress={handleAddToCart}
            containerStyle={{
              width: '100%',
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default WishlistProductContainer;
