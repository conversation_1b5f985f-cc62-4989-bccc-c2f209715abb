import {VariationType} from '../../catalog/types/products';

export interface WishedProductItemType {
  id: string;
  productItemId: string;
  slug: string;
  name: string;
  productId: string;
  image: string;
  prices: PriceType[];
  variations: VariationType[];
  inStock: boolean;
}

export interface WishedProductInResponse {
  id: string;
  productItem: WishedProductItemInResponseType;
}

export interface WishedProductItemInResponseType {
  id: string;
  productId: string;
  barcode: string;
  reference: string;
  name: string;
  image: string | null;
  slug: string;
  images?: string[];
  prices: PriceInResponseType[];
  inStock: boolean;
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  currency: string;
  regularPrice: number;
  promotionalPrice: number;
}
