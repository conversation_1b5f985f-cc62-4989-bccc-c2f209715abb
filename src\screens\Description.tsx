import React from 'react';
import {ScrollView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

import {text} from '../text';
import {theme} from '../constants';
import {components} from '../components';
import type {RootStackParamList} from '../types';

type Props = NativeStackScreenProps<RootStackParamList, 'Description'>;

const Description: React.FC<Props> = ({route}): JSX.Element => {
  const {description} = route.params;

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return <components.Header goBack={true} title='Description' />;
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingHorizontal: 20,
          paddingTop: 55,
          paddingBottom: 20,
        }}
      >
        <text.H3 style={{marginBottom: 14}} numberOfLines={1}>
          Spring leather coat
        </text.H3>
        <text.T16>{description}</text.T16>
      </ScrollView>
    );
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default Description;
