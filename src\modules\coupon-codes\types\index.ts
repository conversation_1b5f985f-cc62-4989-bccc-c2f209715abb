export interface CouponCodeInResponseType {
  id: string;
  description: string;
  code: string;
  allowOnPromotions: boolean;
  appliesToAllProducts: boolean;
  freeShipping: boolean;
  productItems: string[];
  unlimitedUsage: boolean;
  maxRedemptions: number;
  maxUsesPerUser: number;
  totalVoucherRedemptionsByUsers: number;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
  period: {
    from: string;
    to: string;
    forever: boolean;
  };
}

export interface CouponCodeType {
  id: string;
  description: string;
  code: string;
  allowOnPromotions: boolean;
  appliesToAllProducts: boolean;
  freeShipping: boolean;
  productItems: string[];
  unlimitedUsage: boolean;
  maxRedemptions: number;
  maxUsesPerUser: number;
  totalVoucherRedemptionsByUsers: number;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
  period: {
    from: string;
    to: string;
    forever: boolean;
  };
}
