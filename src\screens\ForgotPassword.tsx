import React, {useEffect} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {text} from '../text';
import {components} from '../components';
import {useAppNavigation} from '../hooks';
import {useResetPasswordContext} from '../modules/auth/context/reset-password';

const ForgotPassword: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {email, setEmail, warning, submitEmail, isLoading, step, setStep} =
    useResetPasswordContext();

  useEffect(() => {
    if (step === 'code') {
      navigation.navigate('ConfirmationCode');
    }
  }, [step, navigation]);

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Forgot password' goBack={true} />;
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 25,
          paddingBottom: 20,
        }}
      >
        <text.T16 style={{marginBottom: 40}}>
          Please enter your email address. You will receive a link to create a
          new password via email.
        </text.T16>
        <components.InputField
          label='email'
          placeholder='<EMAIL>'
          onChangeText={(text) => setEmail(text)}
          containerStyle={{marginBottom: warning.email ? 40 : 20}}
          value={email}
          warning={warning.email}
        />
        {warning.generalWarning ? (
          <text.T14
            style={{color: '#E82837', marginBottom: 20, textAlign: 'center'}}
          >
            {warning.generalWarning}
          </text.T14>
        ) : null}
        <components.Button
          title={isLoading ? 'Sending...' : 'Send'}
          onPress={submitEmail}
        />
      </KeyboardAwareScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default ForgotPassword;
