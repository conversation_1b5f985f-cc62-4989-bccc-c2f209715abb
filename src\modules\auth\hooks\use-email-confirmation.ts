'use client';
import {useState} from 'react';
import {sendEmailConfirmationLink} from '../services/email-confirmation';
import {useAppNavigation} from '../../../hooks';

export default function useEmailConfirmation(searchParams: {
  [key: string]: string | string[] | undefined;
}) {
  const [resentRefused, setResentRefused] = useState(false);
  const status = searchParams['status'] ? Number(searchParams['status']) : 500;
  const token = searchParams['token'] ? (searchParams['token'] as string) : '';
  const navigation = useAppNavigation();

  function sendLinkAgain() {
    sendEmailConfirmationLink(token).then((res) => {
      if (res.ok) {
        navigation.navigate('TabNavigator');
      } else {
        setResentRefused(true);
      }
    });
  }

  return {sendLinkAgain, resentRefused, status};
}
