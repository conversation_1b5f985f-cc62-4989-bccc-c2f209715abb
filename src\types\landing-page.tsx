export interface HeroSectionImagesInResponse {
  computerImage: string;
  mobileImage: string;
  redirectUrl: string;
}

export interface HeroSectionImages {
  computerImage: string;
  mobileImage: string;
  link: string;
}

export interface LandingPageContentInResponseType {
  id: string;
  name: string;
  images: Array<HeroSectionImagesInResponse>;
  sections: Array<{title: string; description: string; id: string}>;
}

export interface LandingPageContent {
  id: string;
  name: string;
  images: Array<HeroSectionImages>;
  sections: Array<{title: string; description: string; id: string}>;
}
