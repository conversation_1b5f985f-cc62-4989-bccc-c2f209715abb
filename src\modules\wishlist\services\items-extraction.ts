import {AxiosError} from 'axios';
import extractJWTokens from '../../auth/utils/jwt/extract-tokens';
import {refreshToken} from '../../auth/services/refresh-token';
import {CustomError} from '../../../lib/custom-error';
import {WishedProductInResponse} from '../types/products';
import castToWishedItem from '../utils/types-casting/wishlist-items';
import {GET} from '../../../lib/http-methods';

export async function extractWishlistItems() {
  const {access} = await extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET('/favourites/products', headers);
    const wishedItems = (res.data as WishedProductInResponse[]).map(
      (cartItem) => castToWishedItem(cartItem),
    );

    return wishedItems || [];
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(extractWishlistItems);
      if (!res) throw new CustomError('Unauthorized', 401);

      return res;
    } else throw new CustomError('Server Error!', 500);
  }
}
