import { useEffect, useState } from "react";
import usePagination from "@/hooks/use-pagination";
import { useQuery } from "@tanstack/react-query";
import { ProductType } from "../../types/products";
import { PaginationType } from "@/types";
import { usePathname } from "next/navigation";
import { retrieveProductsFromServerSide } from "../../services/products/products-extraction";
import useUrlParams from "@/hooks/use-url-params";
import {
  getSelectedCategories,
  useProductsFilteringStore,
} from "../../store/products-filter";
import useMinMaxPrice from "./use-min-max-price";

interface Params {
  limit: number;
  paginationInUrlIsUsed?: boolean;
}

export default function useProductsFilteringStoreSubmission({
  limit,
  paginationInUrlIsUsed = false,
}: Params) {
  //used to prevent the update of the url before rendering the screen
  const [startFiltering, setStartFiltering] = useState(false);
  const {
    joinedPageData,
    priceRangeFilteringVersion,
    categories,
    search,
    criteria,
    selectedBrands,
    priceRange,
    filterVersion, //filter version is only responsible on the update of categories and brands to trigger the update of the pagination
  } = useProductsFilteringStore((store) => store);

  const selectedCategoriesToFetchData = getSelectedCategories();
  const selectedCategoriesSlugsToFetchData = selectedCategoriesToFetchData.map(
    (cat) => cat.slug
  );
  const selectedCategoriesToUpdateUrl = [
    ...categories.filter((cat) => cat.selected),
    ...categories.flatMap((cat) =>
      cat.subCategories.filter((subCat) => subCat.selected)
    ),
  ];
  const selectedCategoriesSlugsToUpdateUrl = selectedCategoriesToUpdateUrl.map(
    (cat) => cat.slug
  );
  const brandSlugs = selectedBrands.map((brand) => brand.slug);

  const { page, setPage, pagesNumber, setPagesNumber, paginatedListRef } =
    usePagination({
      extractPaginationFromUrl: true,
    }); //we're going to affecct the pagination in url from here bc we're using the url param in the filter
  const pathname = usePathname();
  const { initializeUrlParams, getParamFromUrl, removeParamFromUrl } =
    useUrlParams();

  const { data, isLoading } = useQuery<{
    products: ProductType[];
    pagination: PaginationType;
  } | null>({
    queryKey: [
      "products",
      page,
      selectedBrands,
      selectedCategoriesSlugsToFetchData,
      criteria,
      priceRangeFilteringVersion,
      search,
      pathname,
    ],
    queryFn: () =>
      retrieveProductsFromServerSide({
        page,
        limit,
        brandSlugs: selectedBrands.map((brand) => brand.slug),
        categoriesSlugs: selectedCategoriesSlugsToFetchData,
        priceRange,
        criteria,
        search,
      }),
  });
  const { prices: minMaxPrices } = useMinMaxPrice(); //used to do not update url with those prices

  useEffect(() => {
    setPagesNumber(
      data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
    );
  }, [data]);

  useEffect(() => {
    //if filterVersion === 0 so the filter is not initialized
    //the filter is initialized if we click apply button in range slider or if touch the side bar
    if (
      (filterVersion !== 0 || priceRangeFilteringVersion !== 0) &&
      page !== 1
    ) {
      setPage(1);
    }
  }, [filterVersion, priceRangeFilteringVersion]);

  //updating query params based on the filter
  useEffect(() => {
    //used only on the type page or filter page
    if (startFiltering) {
      const urlUpdatedParams: Record<string, string> = {};

      //categories set up in  the url
      if (
        selectedCategoriesToFetchData &&
        selectedCategoriesToFetchData.length > 0 &&
        !(
          //we don't update url when we're fetching products based on the joined category page
          (
            joinedPageData.category &&
            selectedCategoriesToFetchData.length === 1 &&
            selectedCategoriesToFetchData[0].id === joinedPageData.category.id
          )
        ) && //by default we fetch based on all the categories
        selectedCategoriesToFetchData !== categories
      ) {
        urlUpdatedParams["categoriesSlugs"] =
          selectedCategoriesSlugsToUpdateUrl.join(",");
      }

      //brands set up in the url
      if (!joinedPageData.brand && brandSlugs && brandSlugs.length > 0) {
        urlUpdatedParams["brandsSlugs"] = brandSlugs.join(",");
      }

      //storting criteria set up
      if (criteria !== "priceAsc") {
        //no need to set the default value
        urlUpdatedParams["criteria"] = criteria;
      }

      //setting search if we got it from search bar
      if (search !== "") {
        //no need to set the default value
        urlUpdatedParams["search"] = search;
      }

      if (
        priceRange.length === 2 &&
        (minMaxPrices[0] !== priceRange[0] || minMaxPrices[1] !== priceRange[1])
      ) {
        urlUpdatedParams["priceRange"] = priceRange.join(",");
      }

      if (page !== 1) urlUpdatedParams["page"] = page.toString();

      initializeUrlParams(urlUpdatedParams);
    }
  }, [
    selectedBrands,
    categories,
    criteria,
    priceRangeFilteringVersion,
    search,
    pathname,
    page,
  ]);

  useEffect(() => {
    setTimeout(() => {
      setStartFiltering(true);
    });
  }, []);

  return {
    products: data?.products || [],
    pagination: data?.pagination || null,
    isLoading,
    setPage,
    page,
    pagesNumber,
    paginatedListRef,
  };
}
