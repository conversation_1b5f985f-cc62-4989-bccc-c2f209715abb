"use client";

import { Circle<PERSON>, Heart } from "lucide-react";
import Image from "next/image";
import type { WishedProductItemType } from "../types/products";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { useWishlistStore } from "../store/wishlist-store";
import { formatPrice } from "@/modules/catalog/utils/prices-transformation";
import { useEffect, useState } from "react";
import { useCartStore } from "@/modules/cart/store/cart-store";
import useCurrency from "@/modules/catalog/hooks/use-currency";

interface Props {
  productItem: WishedProductItemType;
}

export default function WishListProductContainer({ productItem }: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const { removeProductItem } = useWishlistStore((store) => store.actions);
  const { addProductItem } = useCartStore((store) => store.actions);
  const { currency } = useCurrency();

  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  const handleAddToCart = () => {
    addProductItem({
      slug: productItem.slug,
      id: productItem.productItemId,
      productId: productItem.productId,
      cartQuantity: 1,
      name: productItem.name,
      prices: productItem.prices,
      image: productItem.image,
      variations: productItem.variations,
      inStock: productItem.inStock,
    });
  };
  return (
    <>
      {/* Desktop Version */}
      <div
        className={`hidden md:group md:flex items-center py-6 border-b border-gray-100 hover:bg-gradient-to-r hover:from-gray-50/50 hover:to-cyan-50/30 transition-all duration-300`}
      >
        {/* Remove Button */}
        <div className="w-12 flex justify-center">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => removeProductItem(productItem.productItemId)}
            className="text-gray-400 hover:text-red-500 hover:bg-red-50 p-2 h-auto rounded-full transition-all duration-200 group-hover:opacity-100 opacity-60"
          >
            <CircleX className="h-4 w-4" />
          </Button>
        </div>

        {/* Product Image */}
        <div className="w-20 h-20 mr-6 relative group">
          <div className="w-full h-full bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden group-hover:shadow-md transition-shadow duration-200">
            <Image
              src={productImage || "/placeholder.svg"}
              alt={productItem.name}
              onError={() => setProductImage("/not-found/product-image.webp")}
              width={80}
              height={80}
              unoptimized
              className="w-full h-full object-cover"
            />
          </div>
          {promotionIsAvailable && (
            <div className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-lg">
              %
            </div>
          )}
        </div>

        {/* Product Name */}
        <div className="flex-1 pr-8">
          <Text
            textStyle="TS6"
            className="text-gray-900 font-medium leading-relaxed hover:text-cyan-600 transition-colors duration-200 cursor-pointer"
          >
            {productItem.name}
          </Text>
          <div className="flex items-center mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Heart className="h-3 w-3 text-red-400 mr-1" />
            <Text textStyle="TS8" className="text-gray-500 text-xs">
              Ajouté à la liste de souhaits
            </Text>
          </div>
        </div>

        {/* Price */}
        <div className="w-44 text-right pr-8">
          {promotionIsAvailable && (
            <div className="text-gray-400 line-through text-sm mb-1 font-medium">
              {`${formatPrice(productItem.prices[0].realPrice)} ${currency}`}
            </div>
          )}
          <div className="text-gray-900 font-bold text-lg">
            {`${formatPrice(
              productItem.prices[0].promotionalPrice
            )} ${currency}`}
          </div>
          {promotionIsAvailable && (
            <div className="text-green-600 text-xs font-medium mt-1">
              Économisez{" "}
              {formatPrice(
                productItem.prices[0].realPrice -
                  productItem.prices[0].promotionalPrice
              )}{" "}
              {currency}
            </div>
          )}
        </div>

        {/* Stock Status */}
        <div className="w-32 text-center pr-6">
          <div className="inline-flex items-center bg-green-50 text-green-700 px-3 py-1.5 rounded-full text-sm font-medium border border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span>Disponible</span>
          </div>
        </div>

        {/* Add to Cart Button */}
        <div className="w-36">
          <Button
            onClick={handleAddToCart}
            className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white px-4 py-2.5 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2 w-full justify-center"
          >
            <span className="text-sm">Ajouter</span>
          </Button>
        </div>
      </div>

      {/* Mobile Version */}
      <div className="md:hidden bg-white rounded-lg border border-gray-200 p-4 mb-3">
        <div className="flex gap-3">
          {/* Product Image */}
          <div className="w-16 h-16 relative flex-shrink-0">
            <Image
              src={productImage || "/placeholder.svg"}
              alt={productItem.name}
              onError={() => setProductImage("/not-found/product-image.webp")}
              width={64}
              height={64}
              unoptimized
              className="w-full h-full object-cover rounded border border-gray-100"
            />
            {promotionIsAvailable && (
              <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                %
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-sm font-medium text-gray-900 line-clamp-2 pr-2">
                {productItem.name}
              </h3>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => removeProductItem(productItem.productItemId)}
                className="text-gray-400 p-1 flex-shrink-0"
              >
                <CircleX className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center justify-between mb-3">
              <div>
                {promotionIsAvailable && (
                  <div className="text-xs text-gray-400 line-through">
                    {formatPrice(productItem.prices[0].realPrice)} {currency}
                  </div>
                )}
                <div className="text-lg font-bold text-gray-900">
                  {formatPrice(productItem.prices[0].promotionalPrice)}{" "}
                  {currency}
                </div>
              </div>
              <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                {productItem.inStock ? "En stock" : "Rupture de stock"}
              </span>
            </div>

            <Button
              onClick={handleAddToCart}
              className="w-full bg-cyan-500 hover:bg-cyan-600 text-white py-2 text-sm"
            >
              Ajouter au panier
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
