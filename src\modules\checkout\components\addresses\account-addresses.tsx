"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import AddressCreation from "@/modules/checkout/components/addresses/form/address-creation";
import useAddressCreation from "@/modules/checkout/hooks/addresses/use-address-creation";
import useAddresses from "@/modules/checkout/hooks/addresses/use-addresses";
import useAddressDeletion from "@/modules/checkout/hooks/addresses/use-address-deletion";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import AddressManagementContainer from "./container/adress-management";

export default function AccountAddresses() {
  const t = useTranslations("accountPage.accountAdress");
  const { formRef, createAddress, isLoading, wrongInputs, warning } =
    useAddressCreation();
  const { addresses, addressesAreLoading } = useAddresses();
  const {
    deleteAddress,
    deletionIsLoading,
    deletionPopUpIsOpen,
    cancelDeletion,
    confirmAddressDeletion,
    warning: deletionWarning,
  } = useAddressDeletion();

  return (
    <div className="flex flex-col space-y-7">
      {addressesAreLoading ? (
        <>
          <Skeleton className="w-[200px] h-10" />

          <div className="flex flex-col border border-gray-light rounded-2xl px-5">
            {Array.from({ length: 5 }).map((_, idx) => (
              <div
                key={idx}
                className={cn(
                  "py-3 border-gray border-opacity-50 flex flex-col space-y-2",
                  {
                    "border-b": idx !== 4,
                  }
                )}
              >
                <Skeleton className="w-[200px] h-7" />
                <div className="flex justify-between space-x-2">
                  <Skeleton className="w-[150px] h-7" />
                  <Skeleton className="w-[80px] h-7" />
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        addresses &&
        addresses.length > 0 && (
          <>
            <Text textStyle="TS2" className="text-primary font-bold text-xl">
              {t("title")}
            </Text>

            <div className="flex flex-col border border-gray-light rounded-2xl px-5">
              {addresses.map((address, idx) => (
                <AddressManagementContainer
                  key={idx}
                  address={address}
                  onDelete={deleteAddress}
                  bottomDelimter={idx !== addresses.length - 1}
                />
              ))}
            </div>
          </>
        )
      )}

      <Text textStyle="TS2" className="text-primary font-bold text-xl">
        {t("subtitle")}
      </Text>

      <AddressCreation
        formIsHidden={false}
        formRef={formRef}
        wrongInputs={wrongInputs}
        warning={warning}
        primaryTheme={false}
        onCreate={createAddress}
        isLoading={isLoading}
      />

      <Modal
        isOpen={deletionPopUpIsOpen}
        title={t("deletionForm.title")}
        description={t("deletionForm.description")}
        closeName={t("buttons.cancel")}
        confirmName={t("buttons.delete")}
        onClose={cancelDeletion}
        onConfirm={confirmAddressDeletion}
        confirmationIsLoading={deletionIsLoading}
        warning={deletionWarning}
      />
    </div>
  );
}
