import {CategoryInResponseType, CategoryType} from '../../types/categories';
import {toCamelCase} from '../text-transformer';

// React Native backend URL for catalog images
const CATALOG_BACKEND_URL = 'https://api-akal.tdg.tn';

export function castToCategoryType(
  categoryInResponse: CategoryInResponseType,
): CategoryType {
  return {
    slug: categoryInResponse.slug,
    name: toCamelCase(categoryInResponse.name),
    description: categoryInResponse.description as string,
    id: categoryInResponse.id,
    image: categoryInResponse.menuImage
      ? `${CATALOG_BACKEND_URL}${categoryInResponse.menuImage}`
      : null,
    bannerImage: categoryInResponse.bannerImage
      ? `${CATALOG_BACKEND_URL}${categoryInResponse.bannerImage}`
      : null,
    numberOfProducts: categoryInResponse.numberOfProducts || 0,
    subCategories: categoryInResponse.subCategories
      ? categoryInResponse.subCategories.map((subCategory) =>
          castToCategoryType(subCategory),
        )
      : [],
  };
}
