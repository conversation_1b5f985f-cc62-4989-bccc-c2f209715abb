"use client";
import EmailStep from "./email-step";
import CodeStep from "./code-step";
import PasswordStep from "./password-step";
import { useResetPasswordContext } from "../../context/reset-password";

export default function ResetPasswordSteps() {
  const { step, startPasswordStep } = useResetPasswordContext();

  return (
    <div className="w-full flex flex-col">
      {/* Email Step */}
      {step === "email" && !startPasswordStep && (
        <div className="w-full">
          <EmailStep />
        </div>
      )}

      {/* Code Step */}
      {step === "code" && !startPasswordStep && (
        <div className="w-full">
          <CodeStep />
        </div>
      )}

      {/* Password Step */}
      {(step === "password" || startPasswordStep) && (
        <div className="w-full">
          <PasswordStep />
        </div>
      )}
    </div>
  );
}
