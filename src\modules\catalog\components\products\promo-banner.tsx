import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
} from 'react-native';
import {theme} from '../../../../constants';

interface BannerProps {
  title?: string;
  symbol?: string;
  buttonText?: string;
  buttonUrl?: string;
  backgroundImageUrl?: string;
  backgroundColor?: string;
  onClick?: () => void;
  children?: React.ReactNode;
}

export default function PromoBanner({
  title,
  symbol = '%',
  buttonText = 'Voir Plus',
  buttonUrl = '#',
  backgroundImageUrl,
  backgroundColor = '#1EAAB4',
  onClick,
  children,
}: BannerProps) {
  return (
    <View style={[styles.container, {backgroundColor}]}>
      {/* Background Pattern */}
      {backgroundImageUrl && (
        <ImageBackground
          source={{uri: backgroundImageUrl}}
          style={styles.backgroundImage}
          resizeMode='cover'
        />
      )}

      {/* Content */}
      <View style={styles.content}>
        {/* Title */}
        {title && <Text style={styles.title}>{title}</Text>}

        {/* Symbol */}
        <Text style={styles.symbol}>{symbol}</Text>

        {/* Custom Content */}
        {children}

        {/* Button */}
        {buttonText && (
          <TouchableOpacity
            onPress={() => {
              if (onClick) onClick();
            }}
            style={styles.button}
          >
            <Text style={styles.buttonText}>{buttonText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: 300,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    minHeight: 200,
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 48,
    zIndex: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
    ...theme.fonts.DMSans_700Bold,
  },
  symbol: {
    fontSize: 64,
    fontWeight: 'bold',
    color: 'white',
    marginVertical: 16,
    textShadowColor: 'rgba(255,255,255,0.7)',
    textShadowOffset: {width: 0, height: 0},
    textShadowRadius: 10,
    ...theme.fonts.DMSans_700Bold,
  },
  button: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingHorizontal: 32,
    paddingVertical: 12,
    marginTop: 16,
  },
  buttonText: {
    color: '#1EAAB4',
    fontWeight: 'bold',
    ...theme.fonts.DMSans_700Bold,
  },
});
