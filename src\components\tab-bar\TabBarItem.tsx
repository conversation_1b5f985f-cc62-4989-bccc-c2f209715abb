import {Text, TouchableOpacity, TextStyle} from 'react-native';
import React from 'react';
import type {PropsWithChildren} from 'react';

import {theme} from '../../constants';
import {TabBarType} from '../../types';
import {setScreen} from '../../store/slices/tabSlice';
import {useAppDispatch, useAppSelector} from '../../hooks';

type Props = PropsWithChildren<{item: TabBarType}>;

const TabBarItem: React.FC<Props> = ({item}): JSX.Element => {
  const dispatch = useAppDispatch();

  // const currentTabScreen = useAppSelector((state) => state.tab.screen);
  // const textColor =
  //   currentTabScreen === item.name
  //     ? theme.colors.mainColor
  //     : theme.colors.secondaryTextColor;

  // const textStyles: TextStyle = {
  //   textAlign: 'center',
  //   marginTop: 7,
  //   color: textColor,
  //   textTransform: 'uppercase',
  //   fontSize: 10,
  //   // ...theme.fonts.LeagueSpartan_600SemiBold,
  // };

  return (
    <TouchableOpacity
      style={{alignItems: 'center'}}
      onPress={() => dispatch(setScreen(item.name))}
    >
      <item.icon />
    </TouchableOpacity>
  );
};

export default TabBarItem;
