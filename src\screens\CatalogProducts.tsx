import React, {useState} from 'react';
import {View, StyleSheet, SafeAreaView, StatusBar} from 'react-native';

// App imports
import {theme} from '../constants';
import {components} from '../components';
import ProductsList from '../modules/catalog/components/products/products-list';

const CatalogProductsScreen: React.FC = (): JSX.Element => {
  const [localSearch, setLocalSearch] = useState('');

  // Handle search
  const handleSearchSubmit = () => {};

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle='dark-content'
        backgroundColor={theme.colors.white}
        translucent={false}
      />

      {/* Header */}
      <components.Header
        title='Products'
        goBack={true}
        basket={true}
        search={true}
      />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <components.InputField
          placeholder='Search products...'
          value={localSearch}
          onChangeText={setLocalSearch}
          containerStyle={styles.searchInput}
          keyboardType='default'
        />
        <components.Button
          title='Search'
          onPress={handleSearchSubmit}
          containerStyle={styles.searchButton}
        />
      </View>

      {/* Products List */}
      <View style={styles.content}>
        <ProductsList type={'default'} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    alignItems: 'center',
    gap: 10,
  },
  searchInput: {
    flex: 1,
    marginRight: 0,
  },
  searchButton: {
    width: 80,
    height: 50,
  },
  content: {
    flex: 1,
  },
});

export default CatalogProductsScreen;
