import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import Image from "next/image";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Dayjs } from "dayjs";
import { Dispatch, SetStateAction } from "react";
import WonCouponCodeDetails from "./won-coupon-code-details";

interface CouponDialogProps {
  amount?: number;
  percentage?: number;
  code: string;
  date?: Dayjs;
  forever: boolean;
  type: string;
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}

export const CouponPopUp = ({
  forever,
  type,
  amount,
  percentage,
  code,
  date,
  isOpen,
  setIsOpen,
}: CouponDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger className="pr-1"></DialogTrigger>
      <DialogContent className="L:w-[70%] w-full max-w-[900px]">
        <ScrollArea className="w-full h-[80vh] pr-3">
          <div className="w-full flex flex-col gap-[30px] items-center rounded-2xl XL:flex-row">
            <div className="hidden S:block flex-1 mx-auto">
              <Image
                src={"/backgrounds/auth.png"}
                alt="Sign-up picture"
                width={435}
                height={710}
                className="h-[80vh] aspect-auto"
                priority
              />
            </div>
            <div className="flex-1 w-full tinyL:w-[400px]">
              <DialogHeader>
                <DialogTitle></DialogTitle>

                <WonCouponCodeDetails
                  amountOrPercentage={percentage ? percentage : amount}
                  code={code}
                  date={date}
                  forever={forever}
                  type={type}
                />
                <DialogDescription></DialogDescription>
              </DialogHeader>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
