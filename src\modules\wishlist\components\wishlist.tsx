"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { useWishlistStore } from "../store/wishlist-store";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { useRouter } from "next/navigation";
import ProductContainer from "@/modules/catalog/components/products/product/container/default";
import useProducts from "@/modules/catalog/hooks/products/use-products";

export default function Wishlist() {
  const t = useTranslations("shared.wishlist");
  const router = useRouter();
  const [isMovingToCart, setIsMovingToCart] = useState(false);
  const {
    state: { wishlistItems },
    actions: { moveAllToCart },
  } = useWishlistStore();

  const { products } = useProducts({
    limit: 8,
  });

  const handleMoveAllToCart = async () => {
    setIsMovingToCart(true);
    try {
      await moveAllToCart();
    } catch (error) {
      console.error("Error moving items to cart:", error);
    } finally {
      setIsMovingToCart(false);
    }
  };

  return (
    <div className="bg-gradient-to-b from-gray-50/50 to-white">
      <div className="container">
        {/* Header Section */}
        <div className="flex items-center justify-between  ">
          <Text textStyle="TS2" className="text-primary">
            {t("title")} ({wishlistItems.length})
          </Text>

          {wishlistItems.length > 0 && (
            <Button
              onClick={handleMoveAllToCart}
              disabled={isMovingToCart}
              className="bg-white border border-primary hover:bg-primary hover:text-white text-primary px-7 py-5  h-[50px] rounded-sm"
            >
              <Text textStyle="TS6">
                {isMovingToCart ? t("movingToCart") : t("moveAllToBag")}
              </Text>
            </Button>
          )}
        </div>

        {/* Products List */}
        {wishlistItems.length > 0 ? (
          <div className="grid grid-cols-1 S:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-4">
            {wishlistItems.map((item) => {
              const product = {
                id: item.productId,
                slug: item.slug,
                name: item.name,
                categoryIds: [],
                brand: null,
                description: null,
                details: null,
                metaContent: null,
                items: [
                  {
                    id: item.productItemId,
                    barcode: "",
                    reference: "",
                    inStock: item.inStock,
                    image: item.image,
                    images: [item.image],
                    prices: item.prices,
                    variations: item.variations,
                  },
                ],
              };
              return <ProductContainer key={item.id} product={product} />;
            })}
          </div>
        ) : (
          <div className="bg-white rounded-xl p-8 text-center">
            <p>
              <Text textStyle="TS5">
                {t.rich("emptyList", {
                  b: (chunk) => <span className="font-semibold">{chunk}</span>,
                })}
              </Text>
            </p>

            <Button
              variant="voirplus"
              className="px-6 py-3 mt-6"
              onClick={() => router.push("/produits/filtres")}
            >
              <Text textStyle="TS6">{t("discoverMore")}</Text>
            </Button>
          </div>
        )}

        {/* Just For You Section */}
        <div className="flex items-center justify-between mb-6 mt-16 regularL:mt-20">
          <Text textStyle="TS2" className="text-primary">
            {t("justForYou.title")} ({products?.length || 0})
          </Text>

          {products && products.length > 0 && (
            <Button
              onClick={() => router.push("/produits/filtres")}
              className="bg-white border border-primary hover:bg-primary hover:text-white text-primary px-7 py-5  h-[50px] rounded-sm"
            >
              <Text textStyle="TS6">{t("justForYou.seeAll")}</Text>
            </Button>
          )}
        </div>

        {products && products.length > 0 ? (
          <div className="grid grid-cols-1 S:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-4">
            {products.map((product) => (
              <ProductContainer key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl p-8 text-center">
            <p>
              <Text textStyle="TS5">
                {t.rich("emptyList", {
                  b: (chunk) => <span className="font-semibold">{chunk}</span>,
                })}
              </Text>
            </p>

            <Button
              variant="voirplus"
              className="px-6 py-3 mt-6"
              onClick={() => router.push("/produits/filtres")}
            >
              <Text textStyle="TS6">{t("discoverMore")}</Text>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
