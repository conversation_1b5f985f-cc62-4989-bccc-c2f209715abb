export interface UserSignUpType {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
}

export interface UserSignInType {
  email: string;
  password: string;
}

export interface UserDataType {
  email: string;
  name: string;
  role: string;
  isAuthenticated: boolean;
  points: number;
}

export interface UserResponseDataType {
  email: string;
  name: string;
  points: string;
}

export interface AuthFormData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  confirmPassword?: string;
}

export interface AuthValidationResult {
  isValid: boolean;
  warnings: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    confirmPassword: string;
    generalWarning: string;
  };
}
