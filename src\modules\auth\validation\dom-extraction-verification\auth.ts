import {getSignUpFormSchema} from '../schemas/auth/sign-up';
import {getSignInFormSchema} from '../schemas/auth/sign-in';
import {
  getSignInWarnings,
  getSignUpWarnings,
} from '../../utils/warnings/input-warning';
import {getGeneralWarning} from '../../utils/warnings/general-warning';

type ResponseType = {
  ok: boolean;
  warning: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    generalWarning: string;
  };
  data: {
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  };
};

export function verifyAuthContent(
  auth: 'signUp' | 'signIn',
  formData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  },
): ResponseType {
  const {email = '', password = '', firstName = '', lastName = ''} = formData;

  const userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  } = {
    email,
    password,
  };

  if (auth === 'signUp') {
    userData.firstName = firstName;
    userData.lastName = lastName;

    const signUpSchema = getSignUpFormSchema();
    const signUpVerification = signUpSchema.safeParse(userData);

    if (!signUpVerification.success) {
      const warningResult = getSignUpWarnings(signUpVerification);

      return {
        ok: false,
        warning: {
          ...warningResult,
          generalWarning: getGeneralWarning(warningResult),
        },
        data: userData,
      };
    }
  } else {
    const signInSchema = getSignInFormSchema();

    const signInVerification = signInSchema.safeParse(userData);

    if (!signInVerification.success) {
      const warningResult = getSignInWarnings(signInVerification);

      return {
        ok: false,
        warning: {
          ...warningResult,
          firstName: '',
          lastName: '',
          generalWarning: getGeneralWarning(warningResult),
        },
        data: userData,
      };
    }
  }

  return {
    ok: true,
    warning: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      generalWarning: '',
    },
    data: userData,
  };
}
