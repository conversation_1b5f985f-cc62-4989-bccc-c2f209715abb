import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types";
import { CouponCodeInResponseType } from "../types";
import castToCouponCode from "../utils/types-casting/coupon-code";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

export async function retrieveCouponsFromServerSide({
  page,
  limit,
}: {
  page: number;
  limit: number;
}) {
  try {
    const { access } = extractJWTokens();
    const headers = {
      Authorization: `Bearer ${access}`,
    };
    const res = await GET(
      `/voucher-codes?page=${page}&limit=${limit}`,
      headers
    );

    return {
      pagination: res.data.pagination as PaginationType,
      coupons: (res.data.data as CouponCodeInResponseType[]).map(
        (couponInResponse) => castToCouponCode(couponInResponse)
      ),
    };
  } catch (error) {
    return null;
  }
}
