import {GET} from '../../../../lib/http-methods';
import {City, CityResponseDataType} from '../../types/addresses';
import {castToCityType} from '../../utils/types-casting/addresses';

export async function retrieveCities(countryCode: string): Promise<City[]> {
  try {
    const res = await GET(`/addresses/cities/${countryCode}`, {});

    return (res.data as CityResponseDataType[]).map((city) =>
      castToCityType(city),
    );
  } catch (error) {
    return [];
  }
}
