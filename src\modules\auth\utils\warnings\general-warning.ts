export function getServerErrorWarning(status: number): string {
  if (status === 400) {
    return 'Invalid data. Please check the fields and try again.';
  } else if (status === 404) {
    return 'User not found. Please verify your information.';
  } else {
    return 'An unexpected error occurred. Please try again later.';
  }
}

export function getPasswordChangementGeneralWarning(passwords: {
  currentPassword: string;
  newPassword: string;
  confirmationPassword: string;
}): string {
  if (
    passwords.currentPassword.length < 8 ||
    passwords.newPassword.length < 8 ||
    passwords.confirmationPassword.length < 8
  ) {
    return 'All passwords must be at least 8 characters long.';
  }

  if (passwords.newPassword !== passwords.confirmationPassword) {
    return 'New password and confirmation do not match.';
  }

  return '';
}

export function getNameChangementGeneralWarning(name: string): string {
  if (name.length < 8) {
    return 'Name must be at least 8 characters long.';
  }
  return '';
}

export function getGeneralWarning(fields: {
  email: string;
  name?: string;
  password: string;
}): string {
  const {email, name = '', password} = fields;

  if (!email || !name || !password) {
    return 'Please fill in all the required fields.';
  }

  if (email && password) {
    return 'Incorrect email or password.';
  }

  if (email) {
    return 'Email is not valid.';
  }

  if (password) {
    return 'Password is incorrect.';
  }

  return '';
}
