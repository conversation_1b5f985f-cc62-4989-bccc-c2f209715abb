import React from 'react';
import {View, Text, ScrollView, TouchableOpacity} from 'react-native';

import {svg} from '../assets/svg';
import {components} from '../components';
import {useAppNavigation} from '../hooks';
import {theme} from '../constants';
import {text} from '../text';
import useAddresses from '../modules/checkout/hooks/addresses/use-addresses';
import useAddressDeletion from '../modules/checkout/hooks/addresses/use-address-deletion';

const MyAddress: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {addresses, addressesAreLoading} = useAddresses();
  const {
    deleteAddress,
    deletionIsLoading,
    deletionPopUpIsOpen,
    cancelDeletion,
    confirmAddressDeletion,
    warning: deletionWarning,
  } = useAddressDeletion();

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header goBack={true} title='My address' />;
  };

  const getAddressIcon = () => {
    return <svg.HomeSvg />;
  };

  const renderContent = () => {
    if (addressesAreLoading) {
      return <components.Loader />;
    }

    if (!addresses || addresses.length === 0) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}
        >
          <text.H4 style={{marginBottom: 10, textAlign: 'center'}}>
            No addresses found
          </text.H4>
          <text.T16
            style={{textAlign: 'center', color: theme.colors.textColor}}
          >
            Add your first address to get started
          </text.T16>
        </View>
      );
    }

    return (
      <ScrollView
        contentContainerStyle={{
          paddingTop: 25,
          paddingBottom: 20,
          paddingLeft: 20,
        }}
      >
        {addresses.map((item, index, array) => {
          const lastItem = index === array.length - 1;

          return (
            <View
              key={item.id}
              style={{
                padding: 20,
                borderWidth: 1,
                borderRightWidth: 0,
                borderTopLeftRadius: 5,
                borderBottomLeftRadius: 5,
                marginBottom: lastItem ? 0 : 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderColor: theme.colors.lightBlue,
              }}
            >
              <View style={{flex: 1}}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 10,
                  }}
                >
                  <View style={{marginRight: 10}}>{getAddressIcon()}</View>
                  <Text
                    style={{
                      ...theme.fonts.H4,
                      color: theme.colors.mainColor,
                    }}
                  >
                    {item.firstName} {item.lastName}
                  </Text>
                </View>
                <Text
                  style={{
                    ...theme.fonts.DMSans_400Regular,
                    fontSize: 12,
                    lineHeight: 12 * 1.5,
                    color: theme.colors.textColor,
                  }}
                  numberOfLines={2}
                >
                  {item.address1}
                  {item.address2 ? `, ${item.address2}` : ''}
                  {item.city ? `, ${item.city.name}` : ''}
                </Text>
              </View>
              <TouchableOpacity
                style={{
                  padding: 10,
                  marginLeft: 10,
                }}
                onPress={() => deleteAddress(item.id)}
              >
                <Text
                  style={{
                    ...theme.fonts.DMSans_500Medium,
                    fontSize: 14,
                    color: '#E82837',
                  }}
                >
                  Delete
                </Text>
              </TouchableOpacity>
            </View>
          );
        })}
      </ScrollView>
    );
  };

  const renderButton = () => {
    return (
      <components.Button
        title='+ add new address'
        onPress={() => navigation.navigate('AddANewAddress')}
        containerStyle={{padding: 20}}
      />
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderButton()}
      {renderHomeIndicator()}

      <components.ConfirmationModal
        isVisible={deletionPopUpIsOpen}
        title='Delete Address'
        description='Are you sure you want to delete this address? This action cannot be undone.'
        confirmText='Delete'
        cancelText='Cancel'
        onConfirm={confirmAddressDeletion}
        onCancel={cancelDeletion}
        isLoading={deletionIsLoading}
        warning={deletionWarning}
      />
    </components.SmartView>
  );
};

export default MyAddress;
