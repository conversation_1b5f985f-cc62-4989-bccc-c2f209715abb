import {useEffect, useState} from 'react';

import {useQueryClient} from '@tanstack/react-query';
import useAuthRefresher from '../context/auth-refresher';
import {verifyAuthContent} from '../validation/dom-extraction-verification/auth';
import {UserSignUpType} from '../types';
import {signUp} from '../services/sign-up';
import {signIn} from '../services/sign-in';
import {useAppNavigation} from '../../../hooks';
import {
  getSignInStatusWarning,
  getSignUpStatusWarning,
} from '../utils/warnings/server-response-warning';
import {useCartStore} from '../../cart/store/cart-store';
import {addCartItemsOnServerSide} from '../../cart/services/cart-items-addition';

interface AuthWarnings {
  email: string;
  firstName?: string;
  lastName?: string;
  password: string;
  generalWarning: string;
}

export default function useAuth(auth: 'signIn' | 'signUp') {
  const [isLoading, setIsLoading] = useState(false);
  const {refreshUserAuthentication} = useAuthRefresher();
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const queryClient = useQueryClient();
  const [warning, setWarning] = useState<AuthWarnings>({
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    generalWarning: '',
  });
  const navigation = useAppNavigation();
  const cartItems = useCartStore((store) => store.state.cartItems);

  function submitInfo() {
    const verificationResult = verifyAuthContent(auth, {
      email,
      password,
      firstName,
      lastName,
    });

    setWarning(verificationResult.warning);

    //start sign in or sign up process on server side
    if (verificationResult.ok) {
      setIsLoading(true);

      if (auth == 'signUp') {
        signUp(verificationResult.data as UserSignUpType).then((res) => {
          if (res.ok) {
            signIn({
              email: verificationResult.data.email,
              password: verificationResult.data.password,
            }).then(() => {
              //display the confirmation page

              //set email to be displayed in the confirmation page
              setEmail(verificationResult.data.email);

              //cart items addition to server
              const cartItemsToAdd = cartItems.map((cartItem) => ({
                id: cartItem.id,
                quantity: cartItem.cartQuantity,
              }));

              addCartItemsOnServerSide(cartItemsToAdd).catch((error) => {});

              //section changemennt if we've account confirmation phase

              //refresh user authentication state
              refreshUserAuthentication();
              queryClient.invalidateQueries({queryKey: ['user-data']});

              setIsLoading(false);
              navigation.navigate('TabNavigator');
            });
          } else {
            const warning = getSignUpStatusWarning(res.status);

            setWarning({
              email: warning.email as string,
              password: '',
              generalWarning: warning.generalWarning,
            });
            setIsLoading(false);
          }
        });
      } else {
        signIn(verificationResult.data).then((res) => {
          if (res.ok) {
            // push cart items from locale storage to the server
            // redirect user to home page

            //cart items addition to server and deletion from localstorage if we've cart app

            //refresh user authentication state
            refreshUserAuthentication();

            queryClient.invalidateQueries({queryKey: ['user-data']});

            navigation.navigate('TabNavigator');
          } else {
            const warning = getSignInStatusWarning(res.status);
            setWarning({
              email: '',
              password: '',
              generalWarning: warning,
            });
          }

          setIsLoading(false);
        });
      }
    }
  }

  //in case we change from sign in to sign up
  useEffect(() => {
    setWarning({
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      generalWarning: '',
    });
  }, [auth]);

  return {
    warning,
    submitInfo,
    isLoading,
    firstName,
    setFirstName,
    lastName,
    setLastName,
    confirmPassword,
    setConfirmPassword,
    password,
    email,
    setPassword,
    setEmail,
  };
}
