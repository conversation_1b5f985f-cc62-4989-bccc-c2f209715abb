import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import useDetailedCouponBreakdown from "../hooks/use-detailed-coupon-breakdown";

export default function DetailedCouponBreakdown() {
  const t = useTranslations("checkoutPage.orderSummary");
  const { currency } = useCurrency();
  const { productDiscounts, totalDiscount, hasAnyDiscounts } =
    useDetailedCouponBreakdown();

  if (!hasAnyDiscounts) {
    return null;
  }

  return (
    <div className="space-y-2">
      <div className="ml-4 space-y-1">
        {productDiscounts
          .filter(
            (product) =>
              product.couponApplied && product.totalDiscountAmount > 0
          )
          .map((product, index) => (
            <div key={index} className="flex justify-between items-center">
              <Text textStyle="TS7" className="text-gray-600">
                <span
                  className="truncate max-w-[200px]"
                  title={product.productName}
                >
                  {product.productName}
                  {product.quantity > 1 && ` (x${product.quantity})`}
                </span>
              </Text>
              <Text textStyle="TS7" className="text-gray-600">
                -{product.totalDiscountAmount.toFixed(3)} {currency}
              </Text>
            </div>
          ))}
      </div>
    </div>
  );
}
