import {
  WishedProductInResponse,
  WishedProductItemType,
} from '../../types/products';

export default function castToWishedItem(
  productInResponse: WishedProductInResponse,
): WishedProductItemType {
  return {
    slug: productInResponse.productItem.slug,
    productId: productInResponse.productItem.productId,
    id: productInResponse.id, // Use the wishlist item ID from the root
    productItemId: productInResponse.productItem.id, // Use the product item ID from productItem
    name: productInResponse.productItem.name || 'Unknown Product',
    image: productInResponse.productItem.image
      ? `${process.env.BACKEND_ADDRESS}${productInResponse.productItem.image}`
      : '/not-found/product-image.webp',
    variations: [],
    prices:
      productInResponse.productItem.prices?.map((price) => {
        return {
          promotionalPrice: Number(price.promotionalPrice) || 0,
          realPrice: Number(price.regularPrice) || 0,
          currency: price.currency || 'TND',
        };
      }) || [],
    inStock: productInResponse.productItem.inStock ?? false,
  };
}
