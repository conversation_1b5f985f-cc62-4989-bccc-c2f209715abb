import React from 'react';
import {View, ScrollView} from 'react-native';

import {text} from '../text';
import {svg} from '../assets/svg';
import {components} from '../components';
import type {RootStackParamList} from '../types';
import {useGetProductsQuery} from '../store/slices/apiSlice';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'OrderSuccessful'>;

const OrderSuccessful: React.FC<Props> = ({navigation}): JSX.Element => {
  const {
    data: productsData,
    error: productsError,
    isLoading: productsLoading,
  } = useGetProductsQuery();

  const products = productsData instanceof Array ? productsData : [];

  if (productsLoading) {
    return <components.Loader />;
  }

  const renderStatusBar = (): JSX.Element => {
    return <components.StatusBar />;
  };

  const renderContent: () => JSX.Element = () => {
    const scrollViewStyle: object = {
      flexGrow: 1,
      paddingHorizontal: 20,
      justifyContent: 'center',
    };

    return (
      <ScrollView
        contentContainerStyle={{...scrollViewStyle}}
        showsVerticalScrollIndicator={false}
      >
        <svg.CheckSvg />
        <text.H2
          style={{
            marginTop: 30,
            marginBottom: 14,
          }}
        >
          Thank you for{'\n'}your order!
        </text.H2>
        <text.T16>
          Your order will be delivered on time.{'\n'}Thank you!
        </text.T16>
      </ScrollView>
    );
  };

  const renderFooter: () => JSX.Element = () => {
    return (
      <View style={{padding: 20}}>
        <components.Button
          title='View orders'
          onPress={() => {
            navigation.navigate('OrderHistory');
          }}
          containerStyle={{marginBottom: 14}}
        />
        <components.Button
          title='Continue Shopping'
          onPress={() => {
            navigation.navigate('Shop', {
              title: 'Shop',
              products: products,
            });
          }}
          transparent={true}
        />
      </View>
    );
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderContent()}
      {renderFooter()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default OrderSuccessful;
