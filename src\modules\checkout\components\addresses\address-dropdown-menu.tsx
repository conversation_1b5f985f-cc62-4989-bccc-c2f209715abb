import {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
} from 'react-native';
import {theme} from '../../../../constants';

interface Props {
  data: {code: string; name: string}[];
  selectedElement: {code: string; name: string};
  onChange: (value: string) => void;
  primaryTheme?: boolean;
}

export default function AddressDropdownMenu({
  data,
  selectedElement,
  onChange,
  primaryTheme = true,
}: Props) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);

  const handleItemSelect = (item: {code: string; name: string}) => {
    onChange(item.code);
    setMenuIsOpen(false);
  };

  const renderItem = ({item}: {item: {code: string; name: string}}) => {
    const isSelected = selectedElement.code === item.code;

    return (
      <TouchableOpacity
        style={[
          styles.dropdownItem,
          isSelected && styles.selectedItem,
          primaryTheme && isSelected && styles.primarySelectedItem,
        ]}
        onPress={() => handleItemSelect(item)}
      >
        <Text
          style={[
            styles.itemText,
            isSelected && styles.selectedItemText,
            primaryTheme && isSelected && styles.primarySelectedItemText,
          ]}
        >
          {item.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.trigger,
          primaryTheme ? styles.primaryTrigger : styles.grayTrigger,
        ]}
        onPress={() => setMenuIsOpen(true)}
      >
        <View style={styles.triggerContent}>
          <Text style={styles.triggerText}>{selectedElement.name}</Text>
          <Text style={styles.chevron}>▼</Text>
        </View>
      </TouchableOpacity>

      <Modal
        visible={menuIsOpen}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setMenuIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuIsOpen(false)}
        >
          <View style={styles.modalContent}>
            <FlatList
              data={data}
              renderItem={renderItem}
              keyExtractor={(item) => item.code}
              style={styles.dropdown}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  trigger: {
    borderWidth: 1,
    borderRadius: 15,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.white,
    minHeight: 50,
    justifyContent: 'center',
  },
  primaryTrigger: {
    borderColor: theme.colors.mainColor,
  },
  grayTrigger: {
    borderColor: theme.colors.lightBlue,
  },
  triggerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  triggerText: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 16,
    color: theme.colors.mainColor,
    flex: 1,
  },
  chevron: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 12,
    color: theme.colors.textColor,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: theme.colors.white,
    borderRadius: 15,
    maxHeight: 300,
    width: '80%',
    maxWidth: 400,
  },
  dropdown: {
    borderRadius: 15,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.lightBlue,
  },
  selectedItem: {
    backgroundColor: theme.colors.lightBlue,
  },
  primarySelectedItem: {
    backgroundColor: `${theme.colors.mainColor}10`,
  },
  itemText: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 16,
    color: theme.colors.mainColor,
  },
  selectedItemText: {
    ...theme.fonts.DMSans_500Medium,
    color: theme.colors.mainColor,
  },
  primarySelectedItemText: {
    color: theme.colors.mainColor,
  },
});
