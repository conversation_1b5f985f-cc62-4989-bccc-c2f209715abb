import {useEffect} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Picker} from '@react-native-picker/picker';
import {SortingCriteria} from './filter-options/sorting-criteria-dropdown';
import {useProductsFilteringStore} from '../../store/products-filter';
import {CriteriaType} from '../../types';
import {theme} from '../../../constants';

export default function SortingOptions() {
  const {criteria, setCriteria} = useProductsFilteringStore();

  const handleCriteriaChangement = (criteria: CriteriaType) => {
    setCriteria(criteria);
  };

  // Initialize with default sorting
  useEffect(() => {
    if (!criteria) {
      setCriteria('priceAsc');
    }
  }, []);

  const getSortingLabel = (value: string) => {
    const option = SortingCriteria.find((opt) => opt.value === value);
    return option ? option.label : 'Prix croissant';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Trier par:</Text>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={criteria}
          onValueChange={(value: CriteriaType) =>
            handleCriteriaChangement(value)
          }
          style={styles.picker}
          itemStyle={styles.pickerItem}
        >
          {SortingCriteria.map((option, index) => (
            <Picker.Item
              key={index}
              label={option.label}
              value={option.value}
            />
          ))}
        </Picker>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 16,
    paddingHorizontal: 16,
  },
  label: {
    fontWeight: 'bold',
    color: theme.colors.mainColor,
    marginRight: 8,
    fontSize: 14,
    ...theme.fonts.DMSans_700Bold,
  },
  pickerContainer: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.mainColor,
    minWidth: 150,
  },
  picker: {
    height: 40,
    color: theme.colors.mainColor,
  },
  pickerItem: {
    fontSize: 14,
    color: theme.colors.mainColor,
  },
});
