"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import SimilarProduct from "./similar-product";
import { useCartStore } from "../store/cart-store";
import useCartSimilarProducts from "../hooks/use-similar-products";
import useCarouselIndicator from "@/hooks/use-carousel-indicator";

export default function SimilarProducts() {
  const sharedContent = useTranslations("shared.sections");

  const [cartItem] = useCartStore((store) => store.state.cartItems);
  const { products, productsAreLoading } = useCartSimilarProducts({
    limit: 8,
    similarProductSlug: cartItem ? cartItem.slug : "",
  });

  const { setApi } = useCarouselIndicator({
    timer: 3000,
    autoChangement: true,
  });

  return products && !productsAreLoading ? (
    products.length > 0 ? (
      <div className="max-w-full">
        <Carousel
          opts={{
            loop: true,
            slidesToScroll: 1,
            containScroll: "trimSnaps",
          }}
          setApi={setApi}
          className="w-full flex flex-col pb-4 px-6"
        >
          <div className="mb-4">
            <Text textStyle="TS5" className="text-black font-bold text-center">
              {sharedContent("youMayLike")}
            </Text>
          </div>

          <CarouselContent className="flex ml-2 p-2 gap-3">
            {products.map((product) =>
              product.items.map((productItem) => (
                <CarouselItem
                  key={productItem.id}
                  className="basis-1/2 S:basis-1/3 "
                >
                  <SimilarProduct
                    key={product.id}
                    productItem={{
                      slug: product.slug,
                      id: productItem.id,
                      productId: product.id,
                      cartQuantity: 1,
                      name: product.name,
                      prices: productItem.prices,
                      image: productItem.image,
                      variations: productItem.variations,
                      inStock: productItem.inStock,
                    }}
                  />
                </CarouselItem>
              ))
            )}
          </CarouselContent>
        </Carousel>
      </div>
    ) : null
  ) : null;
}
