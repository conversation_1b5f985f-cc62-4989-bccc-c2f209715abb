"use client";
import Text from "@/styles/text-styles";
import { logout } from "@auth/utils/log-out";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import useAddresses from "@/modules/checkout/hooks/addresses/use-addresses";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import useUserStore from "../../store/user-store";

interface Props {
  menuButtons: { name: string; icon: React.ReactNode; pathname: string }[];
}

export default function AccountMenu({ menuButtons }: Props) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isLoading } = useUserStore((store) => store);
  const { addressesAreLoading } = useAddresses();
  const t = useTranslations("accountPage.menuList");

  return !isLoading || !addressesAreLoading ? (
    <div className="extraL:w-fit w-full flex flex-col">
      <div className="flex flex-col space-y-5">
        {menuButtons.map((btn, idx) => (
          <Button
            key={idx}
            className={cn(
              "outline-none w-full px-5 py-6 L:px-5 L:py-8 flex space-x-2 justify-between rounded-2xl hover:bg-white hover:text-primary bg-white text-primary items-center border border-gray-light active:opacity-80",
              {
                "bg-primary text-white hover:bg-primary hover:text-white":
                  btn.pathname === pathname,
              }
            )}
            onClick={() => router.push(btn.pathname)}
          >
            <div className="flex items-center space-x-[10px]">
              {btn.icon}
              <Text textStyle="TS6" className="text-nowrap">
                {t(btn.name)}
              </Text>
            </div>
          </Button>
        ))}
      </div>

      <Button
        className={
          "mt-12 extraL:w-full w-fit flex extraL:mx-0 mx-3 extraL:px-2 px-6 py-7 rounded-xl active:scale-95 duration-300 bg-primary text-white"
        }
        onClick={() => logout(router)}
      >
        <Text textStyle="TS4" className=" text-start text-nowrap">
          {t("logout")}
        </Text>
      </Button>
    </div>
  ) : (
    <div className="px-2 extraL:w-fit w-full flex flex-col">
      <div className={"extraL:flex hidden px-2 py-3 space-x-[10px]"}>
        <Skeleton className="h-11 w-11 rounded-full" />
        <Skeleton className="h-11 w-52" />
      </div>
      <div className="flex flex-col space-y-1">
        {Array.from({ length: 3 }).map((_, idx) => (
          <Skeleton key={idx} className="h-16 w-full" />
        ))}
      </div>

      <Skeleton className="mt-12 h-14 w-full" />
    </div>
  );
}
