import { useWishlistStore } from "../store/wishlist-store";
import { WishedProductItemType } from "../types/products";

export const isProductInWishlist = (productItemId: string): boolean => {
  return useWishlistStore.getState().actions.isProductInWishlist(productItemId);
};

export const toggleProductInWishlist = (
  product: WishedProductItemType
): void => {
  useWishlistStore.getState().actions.toggleProductItem(product);
};

export const createWishlistItem = (
  product: any,
  productItemIndex: number = 0
): WishedProductItemType => {
  return {
    slug: product.slug,
    id: `temp-${product.items[productItemIndex].id}`,
    productItemId: product.items[productItemIndex].id,
    productId: product.id,
    name: product.name,
    prices: product.items[productItemIndex].prices,
    image: product.items[productItemIndex].image,
    variations: product.items[productItemIndex].variations,
    inStock: product.items[productItemIndex].inStock,
  };
};
