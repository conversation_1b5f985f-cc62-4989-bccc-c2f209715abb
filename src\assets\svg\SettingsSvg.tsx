import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

const SettingsSvg: React.FC = (): JSX.Element => {
  return (
    <Svg width={16} height={16} fill='none'>
      <Path
        stroke='#4A5F73'
        strokeLinecap='round'
        strokeWidth={1.2}
        d='M2 3.333h8m4 9.334h-2.667H14Zm-12 0h6.667H2ZM14 8H6h8ZM2 8h1.333H2Zm12-4.667h-1.333H14ZM10 11.333A1.333 1.333 0 1 0 10 14a1.333 1.333 0 0 0 0-2.667ZM4.667 6.667a1.333 1.333 0 1 0 0 2.666 1.333 1.333 0 0 0 0-2.666ZM11.333 2a1.333 1.333 0 1 0 0 2.667 1.333 1.333 0 0 0 0-2.667Z'
      />
    </Svg>
  );
};

export default SettingsSvg;
