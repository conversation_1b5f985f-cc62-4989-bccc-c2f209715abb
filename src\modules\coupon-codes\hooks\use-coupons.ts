import { keepPreviousData, useQuery } from "@tanstack/react-query";
import usePagination from "@/hooks/use-pagination";
import { useEffect } from "react";
import { PaginationType } from "@/types";
import { CouponCodeType } from "../types";
import { retrieveCouponsFromServerSide } from "../services/coupons-extraction";

export default function useCoupons(limit: number) {
  const { page, setPage, pagesNumber, setPagesNumber } = usePagination();

  const { data, isLoading, isError } = useQuery<{
    coupons: CouponCodeType[];
    pagination: PaginationType;
  } | null>({
    queryKey: ["coupons", page],
    queryFn: () =>
      retrieveCouponsFromServerSide({
        page,
        limit,
      }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    if (data?.pagination && pagesNumber !== data?.pagination?.totalPages)
      setPagesNumber(data.pagination.totalPages);
  }, [data]);

  return {
    coupons: data?.coupons || [],
    couponsAreLoading: isLoading,
    couponsError: isError,
    setPage,
    page,
    pagesNumber,
  };
}
