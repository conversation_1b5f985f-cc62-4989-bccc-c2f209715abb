import {View} from 'react-native';
import React, {useEffect} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {components} from '../components';
import useUser from '../modules/auth/hooks/use-user';
import useNameChangement from '../modules/auth/hooks/use-name-changement';
import {text} from '../text';

const EditProfile: React.FC = (): JSX.Element => {
  const {user} = useUser();

  const {submitName, isLoading, warning, setNewName, newName} =
    useNameChangement();

  useEffect(() => {
    if (user) {
      setNewName(user.name || '');
    }
  }, [user]);

  const renderStatusBar: () => JSX.Element = () => {
    return <components.StatusBar />;
  };

  const renderHeader: () => JSX.Element = () => {
    return <components.Header title='Edit profile' goBack={true} />;
  };

  const renderForm: () => JSX.Element = () => {
    return (
      <View>
        <text.T16 style={{marginBottom: 40, color: 'red'}}>
          {warning.getGeneralWarning}
        </text.T16>
        <components.InputField
          label='name'
          placeholder='Enter your name'
          value={newName}
          containerStyle={{marginBottom: 20}}
          onChangeText={(text: string) => setNewName(text)}
          check={false}
        />
        <components.InputField
          label='email'
          placeholder='Email address'
          value={user?.email || ''}
          containerStyle={{marginBottom: 20}}
          check={false}
          disabled={true}
        />
      </View>
    );
  };

  const renderButton: () => JSX.Element = () => {
    return (
      <components.Button
        title={isLoading ? 'Saving...' : 'save changes'}
        onPress={submitName}
      />
    );
  };

  const renderContent: () => JSX.Element = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          flexGrow: 1,
          paddingTop: 55,
          paddingBottom: 20,
        }}
      >
        {renderForm()}
        {renderButton()}
      </KeyboardAwareScrollView>
    );
  };

  const renderHomeIndicator: () => JSX.Element = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default EditProfile;
