import { CriteriaType } from "../types";

// Simplified variant types for React Native
export type ProductsSectionsVariantRN = 
  | "news" 
  | "similarProducts" 
  | "recommended" 
  | "mostSold"
  | "all";

export function getCriteriaBasedOnProductsVariantRN(
  variant: ProductsSectionsVariantRN
): CriteriaType {
  switch (variant) {
    case "news":
      return "createdAtDesc";
    case "similarProducts":
      return "displayOrder";
    case "recommended":
      return "displayOrder";
    case "mostSold":
      return "mostSold";
    case "all":
    default:
      return "displayOrder";
  }
}
