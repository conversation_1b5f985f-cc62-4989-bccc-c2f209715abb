import {View, Text, useColorScheme} from 'react-native';
import React from 'react';

import {theme} from '../constants';
import {useAppSelector} from '../hooks';

type Props = {
  children: React.ReactNode;
  style?: object;
  numberOfLines?: number;
};

const H2: React.FC<Props> = ({children, style, numberOfLines}): JSX.Element => {
  const colorScheme = useColorScheme();

  return (
    <Text
      style={{
        color:
          colorScheme === 'dark' ? theme.colors.white : theme.colors.darkBlue,
        textTransform: 'capitalize',
        ...theme.fonts.H2,
        ...style,
      }}
      numberOfLines={numberOfLines}
    >
      {children}
    </Text>
  );
};

export default H2;
