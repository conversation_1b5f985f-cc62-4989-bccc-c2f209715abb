import {
  ItemInResponseType,
  ItemType,
  PriceRange,
  PriceRangeInResponse,
  ProductInResponseType,
  ProductType,
} from '../../types/products';
import {castToMetaContentType} from './meta-content';

// React Native backend URL for catalog images
const CATALOG_BACKEND_URL = 'https://api-akal.tdg.tn';
// Fallback image for React Native
const FALLBACK_IMAGE =
  'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image';

export function castToProductType(
  productInResponse: ProductInResponseType,
): ProductType {
  return {
    metaContent: castToMetaContentType(productInResponse.metaContent),
    slug: productInResponse.slug,
    brand: productInResponse.brand
      ? {
          slug: productInResponse.brand.slug,
          name: productInResponse.brand.name,
          image: productInResponse.brand.image
            ? `${CATALOG_BACKEND_URL}${productInResponse.brand.image}`
            : undefined,
        }
      : null,
    details: productInResponse.details,
    categoryIds: productInResponse.categoryIds,
    name: productInResponse.name as string,
    description: productInResponse.description as string,
    id: productInResponse.id,
    items: productInResponse.items.map((item) => castToItemType(item)),
  };
}

function castToItemType(item: ItemInResponseType): ItemType {
  return {
    id: item.id,
    reference: item.reference,
    barcode: item.barcode,
    image:
      item.image && item.image != 'null'
        ? `${CATALOG_BACKEND_URL}${item.image}`
        : FALLBACK_IMAGE,
    images: item.images.map((image) =>
      image && image != 'null'
        ? `${CATALOG_BACKEND_URL}${image}`
        : FALLBACK_IMAGE,
    ),
    variations: item.variations || [],
    prices: item.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: item.inStock,
    promotion: item.promotion,
  };
}

export function castToPriceRange(
  priceRangeInResponse: PriceRangeInResponse,
): PriceRange {
  return {
    minPrice: Number(priceRangeInResponse.min),
    maxPrice: Number(priceRangeInResponse.max),
  };
}
