"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import CheckMark from "@assets/icons/check-mark";
import XSymbolIcon from "@assets/icons/x-symbol";
import useOrderDetail from "../../hooks/user-order-detail";
import { useTranslations } from "next-intl";
import Link from "next/link";
import OrderConfirmationContainer from "./container/confirmation";

interface Props {
  orderId: string;
}

export default function OrderConfirmation({ orderId }: Props) {
  const { order, isLoading } = useOrderDetail(orderId);
  const t = useTranslations("ordersManagement");

  return !isLoading ? (
    <div className="w-full px-4 pt-4 flex flex-col space-y-4 justify-center items-center text-white mt-16">
      <div className="w-[116px] h-[116px] bg-primary bg-opacity-20 rounded-full flex justify-center items-center">
        <div className="w-[96px] h-[96px] bg-primary bg-opacity-50 rounded-full flex justify-center items-center">
          <div className="w-[76px] h-[76px] bg-primary rounded-full flex justify-center items-center">
            {order ? <CheckMark /> : <XSymbolIcon />}
          </div>
        </div>
      </div>
      <div
        className={cn("L:w-[80%] w-full flex flex-col items-center space-y-6")}
      >
        <Text textStyle="TS3" className="text-primary font-bold text-center">
          {order ? t("confirmation.title") : t("unavailable.title")}
        </Text>
        <Text textStyle="TS4" className="text-primary text-center ">
          {order ? t("confirmation.description") : t("unavailable.description")}
        </Text>
        {order && <OrderConfirmationContainer details={order} />}
        <Link
          href="/"
          className="bg-primary border hover:bg-white hover:text-primary border-primary p-3 rounded-3xl"
        >
          <Text textStyle="TS6">{t("buttons.comeBackHome")}</Text>
        </Link>
      </div>
    </div>
  ) : (
    <div className="w-full px-4 pt-4 flex flex-col space-y-10 justify-center items-center text-white">
      <Skeleton className="w-[116px] h-[116px] rounded-full" />
      <div
        className={cn("L:w-[80%] w-full flex flex-col items-center space-y-9")}
      >
        <Skeleton className="h-10 max-w-40 w-full" />
        <div className="w-full flex flex-col items-center space-y-1">
          {Array.from({ length: 5 }).map((_, idx) => (
            <Skeleton key={idx} className="h-8 max-w-96 w-full" />
          ))}
        </div>
      </div>
    </div>
  );
}
