import React from 'react';
import {SafeAreaView as SafeAreaViewRN} from 'react-native-safe-area-context';
import type {PropsWithChildren} from 'react';

import {theme} from '../../constants';

type Props = PropsWithChildren<{
  style?: object;
  transparent?: boolean;
  children: React.ReactNode;
  edges?: ['top'] | ['top', 'bottom'] | ['bottom'];
}>;

const SafeAreaView: React.FC<Props> = ({
  children,
  edges,
  style,
  transparent,
}): JSX.Element => {
  return (
    <SafeAreaViewRN
      style={{
        flex: 1,
        backgroundColor: transparent
          ? theme.colors.transparent
          : theme.colors.white,
        ...style,
      }}
      edges={edges}
    >
      {children}
    </SafeAreaViewRN>
  );
};

export default SafeAreaView;
