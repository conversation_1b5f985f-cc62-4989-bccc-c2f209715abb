import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import useScreenSize from "@/hooks/use-screen-size";
import Text from "@/styles/text-styles";
import React from "react";

interface Props {
  title: string;
  children: React.ReactNode[] | React.ReactNode;
  count?: number;
}

export default function FilterChoiceContainer({
  title,
  children,
  count,
}: Props) {
  const doubleExtraL = 900;
  const { width } = useScreenSize();

  return width > doubleExtraL ? (
    <div className=" border-b border-gray/10 pt-5 pb-12">
      <div className="flex flex-col space-y-5">
        <div>
          <h3 className="flex gap-2">
            <Text textStyle="TS5" className="text-black font-bold">
              {title}{" "}
              <span className="text-blue font-thin">
                {count && count > 0 && `(${count})`}
              </span>
            </Text>
          </h3>
        </div>
        <ScrollArea>
          <div className="space-y-1 max-h-[250px] pr-2">{children}</div>
        </ScrollArea>
      </div>
    </div>
  ) : (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1" className="border-b border-gray/40">
        <AccordionTrigger>
          <h3 className="flex gap-2">
            <Text textStyle="TS6" className="text-black font-semibold">
              {title}
            </Text>
          </h3>
        </AccordionTrigger>
        <AccordionContent className="px-4">
          <ScrollArea className="max-h-[200px]">
            <div className="space-y-1 pr-2">{children}</div>
          </ScrollArea>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
