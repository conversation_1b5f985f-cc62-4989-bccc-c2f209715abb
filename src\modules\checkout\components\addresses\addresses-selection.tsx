import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import useAddresses from '../../hooks/addresses/use-addresses';
import useAddressSelection from '../../store/address-selection-store';
import {AddressType} from '../../types/addresses';
import {theme} from '../../../../constants';

interface AddressSelectionContainerProps {
  address: AddressType;
  isSelected: boolean;
  onSelect: () => void;
}

const AddressSelectionContainer: React.FC<AddressSelectionContainerProps> = ({
  address,
  isSelected,
  onSelect,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.addressContainer,
        isSelected && styles.selectedAddressContainer,
      ]}
      onPress={onSelect}
    >
      <View style={styles.addressContent}>
        <View
          style={[styles.radioButton, isSelected && styles.selectedRadioButton]}
        >
          {isSelected && <View style={styles.radioButtonInner} />}
        </View>
        <View style={styles.addressInfo}>
          <Text
            style={[
              styles.addressName,
              isSelected && styles.selectedAddressName,
            ]}
          >
            {`${address.firstName} ${address.lastName}`}
          </Text>
          <Text
            style={[
              styles.addressDetails,
              isSelected && styles.selectedAddressDetails,
            ]}
          >
            {`${address.address1} ${address.city?.name} ${
              address.postalCode ? address.postalCode : ''
            }`}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const AddressSelectionContainerSkeleton: React.FC = () => {
  return (
    <View style={styles.addressContainer}>
      <View style={styles.addressContent}>
        <View style={styles.skeletonRadio} />
        <View style={styles.addressInfo}>
          <View style={styles.skeletonName} />
          <View style={styles.skeletonDetails} />
        </View>
      </View>
    </View>
  );
};

export default function AddressesSelection() {
  const {addresses, addressesAreLoading} = useAddresses();
  const {selectedAddressId, selectAddress} = useAddressSelection();

  if (addressesAreLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.skeletonTitle} />
        <View style={styles.addressList}>
          {Array.from({length: 3}).map((_, idx) => (
            <AddressSelectionContainerSkeleton key={idx} />
          ))}
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.addressList}>
        {addresses?.map((address) => (
          <AddressSelectionContainer
            key={address.id}
            address={address}
            isSelected={address.id === selectedAddressId}
            onSelect={() => selectAddress(address.id)}
          />
        ))}

        <TouchableOpacity
          style={[
            styles.addressContainer,
            selectedAddressId === '' && styles.selectedAddressContainer,
          ]}
          onPress={() => selectAddress('')}
        >
          <View style={styles.addressContent}>
            <View
              style={[
                styles.radioButton,
                selectedAddressId === '' && styles.selectedRadioButton,
              ]}
            >
              {selectedAddressId === '' && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
            <View style={styles.addressInfo}>
              <Text
                style={[
                  styles.addressName,
                  selectedAddressId === '' && styles.selectedAddressName,
                ]}
              >
                Add New Address
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  addressList: {
    gap: 16,
  },
  addressContainer: {
    borderWidth: 1,
    borderColor: theme.colors.lightBlue,
    borderRadius: 15,
    padding: 16,
    backgroundColor: theme.colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedAddressContainer: {
    borderColor: theme.colors.mainColor,
    backgroundColor: theme.colors.mainColor,
  },
  addressContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.lightBlue,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRadioButton: {
    borderColor: theme.colors.white,
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.white,
  },
  addressInfo: {
    flex: 1,
    gap: 8,
  },
  addressName: {
    ...theme.fonts.DMSans_700Bold,
    fontSize: 18,
    color: theme.colors.mainColor,
  },
  selectedAddressName: {
    color: theme.colors.white,
  },
  addressDetails: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 14,
    color: theme.colors.textColor,
  },
  selectedAddressDetails: {
    color: theme.colors.white,
  },
  skeletonTitle: {
    width: 200,
    height: 24,
    backgroundColor: theme.colors.lightBlue,
    borderRadius: 4,
    marginBottom: 16,
  },
  skeletonRadio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.lightBlue,
  },
  skeletonName: {
    width: 150,
    height: 20,
    backgroundColor: theme.colors.lightBlue,
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonDetails: {
    width: 200,
    height: 16,
    backgroundColor: theme.colors.lightBlue,
    borderRadius: 4,
  },
});
