import React, {useEffect, useState} from 'react';
import {View, Text, Image, TouchableOpacity, Alert} from 'react-native';
import {theme} from '../../constants';
import {ProductItemType} from '../../modules/cart/types/products';
import {useCartStore} from '../../modules/cart/store/cart-store';
import {formatPrice} from '../../modules/catalog/utils/prices-transformation';
import useCurrency from '../../modules/catalog/hooks/use-currency';
import {svg} from '../../assets/svg';

interface Props {
  productItem: ProductItemType;
}

const CartProductContainer: React.FC<Props> = ({productItem}) => {
  const [productImage, setProductImage] = useState(
    '/not-found/product-image.webp',
  );

  const {updateProductItemQuantity, removeProductItem} = useCartStore(
    (store) => store.actions,
  );
  const {currency} = useCurrency();

  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  const handleRemoveItem = () => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from your cart?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => removeProductItem(productItem.id),
        },
      ],
    );
  };

  const handleDecreaseQuantity = () => {
    const newQuantity = productItem.cartQuantity - 1;
    if (newQuantity > 0) {
      updateProductItemQuantity(productItem.id, newQuantity);
    }
  };

  const handleIncreaseQuantity = () => {
    const newQuantity = productItem.cartQuantity + 1;
    updateProductItemQuantity(productItem.id, newQuantity);
  };

  return (
    <View
      style={{
        width: '100%',
        flexDirection: 'row',
        gap: 16,
        padding: 16,
        backgroundColor: theme.colors.imageBackground,
        borderRadius: 8,
        marginBottom: 12,
      }}
    >
      {/* Product Image */}
      <View style={{flexShrink: 0}}>
        <Image
          source={{uri: productImage}}
          style={{
            width: 96,
            height: 96,
            borderRadius: 8,
            backgroundColor: theme.colors.white,
          }}
          resizeMode='cover'
          onError={() => setProductImage('/not-found/product-image.webp')}
        />
      </View>

      {/* Product Details */}
      <View style={{flex: 1, justifyContent: 'space-between'}}>
        {/* Product Name */}
        <View style={{marginBottom: 8}}>
          <Text
            style={{
              ...theme.fonts.DMSans_500Medium,
              fontSize: 16,
              lineHeight: 16 * 1.3,
              color: theme.colors.mainColor,
            }}
            numberOfLines={2}
          >
            {productItem.name}
          </Text>
        </View>

        {/* Price */}
        <View style={{marginBottom: 12}}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
            <Text
              style={{
                ...theme.fonts.DMSans_700Bold,
                fontSize: 16,
                lineHeight: 16 * 1.3,
                color: theme.colors.mainColor,
              }}
            >
              {`${formatPrice(
                productItem.prices[0].promotionalPrice,
              )} ${currency}`}
            </Text>
            {promotionIsAvailable && (
              <Text
                style={{
                  ...theme.fonts.DMSans_400Regular,
                  fontSize: 14,
                  lineHeight: 14 * 1.3,
                  color: theme.colors.textColor,
                  textDecorationLine: 'line-through',
                }}
              >
                {`${formatPrice(productItem.prices[0].realPrice)} ${currency}`}
              </Text>
            )}
          </View>
        </View>

        {/* Controls Row */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {/* Quantity Controls */}
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 12}}>
            <TouchableOpacity
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: theme.colors.white,
                borderWidth: 1,
                borderColor: '#EAEAEA',
                justifyContent: 'center',
                alignItems: 'center',
                opacity: productItem.cartQuantity === 1 ? 0.5 : 1,
              }}
              onPress={handleDecreaseQuantity}
              disabled={productItem.cartQuantity === 1}
            >
              <svg.CounterMinusSvg />
            </TouchableOpacity>

            <Text
              style={{
                ...theme.fonts.DMSans_500Medium,
                fontSize: 16,
                lineHeight: 16 * 1.3,
                color: theme.colors.mainColor,
                minWidth: 20,
                textAlign: 'center',
              }}
            >
              {productItem.cartQuantity}
            </Text>

            <TouchableOpacity
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: theme.colors.white,
                borderWidth: 1,
                borderColor: '#EAEAEA',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={handleIncreaseQuantity}
            >
              <svg.CounterPlusSvg />
            </TouchableOpacity>
          </View>

          {/* Delete Button */}
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              backgroundColor: '#F3F6FB',
              borderRadius: 16,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={handleRemoveItem}
          >
            <svg.BasketSvg />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default CartProductContainer;
