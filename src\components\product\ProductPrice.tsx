import React from 'react';
import {View, Text, ViewStyle} from 'react-native';

import {theme} from '../../constants';
import {ProductType} from '../../modules/catalog/types/products';
import useCurrency from '../../modules/catalog/hooks/use-currency';
// import useCurrency from '../../catalog/hooks/use-currency'; // Not available in React Native

type Props = {
  item: ProductType;
  version: 1 | 2;
  containerStyle?: ViewStyle;
  numberOfLines?: number;
};

const ProductPrice: React.FC<Props> = ({
  item,
  containerStyle,
  numberOfLines = 1,
}): JSX.Element => {
  const currency = useCurrency();

  const firstItem = item.items?.[0];
  const firstPrice = firstItem?.prices?.[0];
  const currentPrice =
    firstPrice?.promotionalPrice || firstPrice?.realPrice || 0;
  const oldPrice =
    firstPrice && firstPrice.promotionalPrice < firstPrice.realPrice
      ? firstPrice.realPrice
      : undefined;

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        ...containerStyle,
      }}
    >
      {oldPrice && (
        <Text
          style={{
            marginRight: 4,
            textDecorationLine: 'line-through',
            ...theme.fonts.DMSans_400Regular,
            fontSize: 12,
            color: theme.colors.textColor,
            lineHeight: 12 * 1.5,
          }}
        >
          ${oldPrice.toFixed(2)}
        </Text>
      )}
      <Text
        style={{
          ...theme.fonts.DMSans_500Medium,
          fontSize: 14,
          lineHeight: 14 * 1.5,
          color: theme.colors.mainColor,
        }}
      >
        {currency.currency} {currentPrice.toFixed(2)}
      </Text>
    </View>
  );
};

export default ProductPrice;
