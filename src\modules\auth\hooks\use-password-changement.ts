import {FormEvent, useRef, useState} from 'react';
import {getPasswordChangementWarning} from '../utils/warnings/server-response-warning';
import {CustomError} from '../../../lib/custom-error';
import {useAppNavigation} from '../../../hooks';
import {changePassword} from '../services/password-changement';
import {verifyPasswordChangementData} from '../validation/password-changement-verification';
import {getPasswordChangementGeneralWarning} from '../utils/warnings/general-warning';

export default function usePasswordChangement() {
  const [isLoading, setIsLoading] = useState(false);
  const [passwordChanged, setPasswordChanged] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const navigation = useAppNavigation();
  const [warning, setWarning] = useState({
    currentPassword: '',
    newPassword: '',
    confirmationPassword: '',
    getGeneralWarning: '',
  });

  function submitPassword(event: FormEvent) {
    event.preventDefault();

    if (!formRef.current) return null;

    const formData = new FormData(formRef.current);
    const passwords = {
      currentPassword: formData.get('currentPassword') as string,
      newPassword: formData.get('newPassword') as string,
      confirmationPassword: formData.get('confirmationPassword') as string,
    };
    const passwordVerification = verifyPasswordChangementData(passwords);

    if (!passwordVerification.ok)
      setWarning({
        ...passwordVerification.warning,
        getGeneralWarning: getPasswordChangementGeneralWarning(passwords),
      });
    else {
      setIsLoading(true);

      //clearing all warnings
      setWarning({
        currentPassword: '',
        newPassword: '',
        confirmationPassword: '',
        getGeneralWarning: '',
      });

      //submit verified passwords on server side
      changePassword({
        oldPassword: passwords.currentPassword,
        newPassword: passwords.newPassword,
      })
        .then((res) => {
          setIsLoading(false);
          if (res) {
            if (res.ok) {
              formRef.current?.reset();
              setPasswordChanged(true);

              setTimeout(() => {
                setPasswordChanged(false);
              }, 4000);
            } else {
              const warning = getPasswordChangementWarning(
                res.status,
                res.code as string,
              );
              setWarning({
                currentPassword: warning?.passwordWarning as string,
                newPassword: '',
                confirmationPassword: '',
                getGeneralWarning: warning?.generalWarning as string,
              });
            }
          } //unauthorized user
          else {
            navigation.navigate('TabNavigator');
          }
        })
        .catch((error: CustomError) => {
          const warning = getPasswordChangementWarning(
            error.status,
            error.code as string,
          );
          setWarning({
            currentPassword: warning?.passwordWarning as string,
            newPassword: '',
            confirmationPassword: '',
            getGeneralWarning: warning?.generalWarning as string,
          });
        });
    }
  }

  return {
    isLoading,
    submitPassword,
    formRef,
    warning,
    passwordChanged,
  };
}
