import {
  registerGuestOrderOnServerSide,
  registerUserOrderOnServerSide,
} from "../services/orders/order-creation";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { CustomError } from "../../../utils/custom-error";
import { UserDataType } from "@/modules/auth/types";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import useValidatedCouponCode from "../../coupon-codes/store/coupon-code-validation";
import {
  RegisterGuestOrderRequestParams,
  RegisterUserOrderRequestParams,
} from "../types/orders";

export default function useCheckoutSubmission(user: UserDataType | null) {
  const { state, actions } = useCartStore((store) => store);
  const validatedCouponCode = useValidatedCouponCode(
    (store) => store.validatedCouponCode
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const t = useTranslations("shared.warnings");
  const setAuthDialogState = useAuthDialogState((store) => store.setIsOpen);

  async function checkoutOnServerSide(
    address: Record<string, string> | null,
    notes: string,
    addressId: string
  ) {
    try {
      if (addressId == "" && !address) throw new Error("missedData");

      setIsLoading(true);

      let orderId = "";
      if (user && user.isAuthenticated) {
        const orderInfo: RegisterUserOrderRequestParams = {
          items: state.cartItems.map((item) => ({
            productItemId: item.id,
            quantity: item.cartQuantity,
          })),
          notes,
        };

        if (validatedCouponCode && validatedCouponCode.code !== "")
          orderInfo["voucherCode"] = validatedCouponCode.code;

        if (addressId !== "") orderInfo["addressId"] = addressId;
        else throw new Error("missedData");

        const res = await registerUserOrderOnServerSide(orderInfo);
        orderId = res.id;
      } else if (address) {
        const orderInfo: RegisterGuestOrderRequestParams = {
          items: state.cartItems.map((item) => ({
            productItemId: item.id,
            quantity: item.cartQuantity,
          })),
          address,
          notes,
        };

        if (validatedCouponCode && validatedCouponCode.code !== "")
          orderInfo["voucherCode"] = validatedCouponCode.code;

        const res = await registerGuestOrderOnServerSide(orderInfo);
        orderId = res.id;
      } else throw new Error("missedData");

      actions.emptyCart();
      router.push(`/commandes/${orderId}`);
    } catch (error) {
      const customError = error as CustomError;

      if (customError.message === "missedData") {
        setError(t("missedData"));
        const addressSection = document.getElementById("addressCreation");

        if (addressSection)
          addressSection.scrollIntoView({ behavior: "smooth" });
      } else if (customError.status === 401) setAuthDialogState(true);
      else if (customError.code === "P9000") {
        setError(t("productItemOutOfStock"));
      } else setError(t("serverError"));
    } finally {
      setIsLoading(false);
    }
  }

  return {
    isLoading,
    error,
    checkoutOnServerSide,
  };
}
