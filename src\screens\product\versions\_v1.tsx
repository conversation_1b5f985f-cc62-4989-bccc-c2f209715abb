import React, {useState, PropsWithChildren} from 'react';
import {View, TouchableOpacity, Alert, ScrollView, Image} from 'react-native';

import {text} from '../../../text';
import {svg} from '../../../assets/svg';
import {theme} from '../../../constants';
import {reviews} from '../../../constants';
import {components} from '../../../components';
import {useAppNavigation, useAppDispatch} from '../../../hooks';
import {useGetColorsQuery} from '../../../store/slices/apiSlice';
import {quantityInCart, addedToCartMessage} from '../../../utils';
import {fullRemoveFromCart, addToCart} from '../../../store/slices/cartSlice';
import {ProductType} from '../../../modules/catalog/types/products';

type Props = PropsWithChildren<{item: ProductType}>;

const _v1: React.FC<Props> = ({item}): JSX.Element => {
  const {
    data: colorsData,
    error: colorsError,
    isLoading: colorsIsLoading,
  } = useGetColorsQuery();

  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();
  const quantity = quantityInCart(item) as number;

  const images = item.items[0].images;
  const [currentSlideIndex, setCurrentSlideIndex] = useState<number>(0);

  if (colorsIsLoading) {
    return <components.Loader />;
  }

  const updateCurrentSlideIndex = (e: any): void => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / theme.sizes.width);
    setCurrentSlideIndex(currentIndex);
  };

  const renderCarousel = (): JSX.Element => {
    const renderImages = () => {
      return (
        <ScrollView
          onMomentumScrollEnd={updateCurrentSlideIndex}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          contentContainerStyle={{backgroundColor: '#F5F9FC'}}
          bounces={false}
          alwaysBounceHorizontal={false}
        >
          {images.map((item: any, index: any) => {
            return (
              <Image
                key={index}
                source={{uri: item}}
                style={{width: theme.sizes.width, aspectRatio: 0.75}}
                resizeMode='cover'
              />
            );
          })}
        </ScrollView>
      );
    };

    const renderDots = (): JSX.Element | null => {
      if (images.length > 1) {
        return (
          <View
            style={{
              height: 24,
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              bottom: 30,
              flexDirection: 'row',
              alignSelf: 'center',
            }}
          >
            {images.map((image, index, array) => {
              return (
                <View
                  key={index}
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: 10 / 2,
                    backgroundColor:
                      currentSlideIndex === index
                        ? theme.colors.mainColor
                        : theme.colors.white,
                    opacity: currentSlideIndex === index ? 1 : 0.5,
                    borderWidth: 1,
                    borderColor:
                      currentSlideIndex === index
                        ? theme.colors.mainColor
                        : '#DBE9F5',
                    marginHorizontal: 4,
                  }}
                />
              );
            })}
          </View>
        );
      }
      return null;
    };

    const renderInWishList = () => {
      return (
        <View
          style={{
            height: 24,
            width: 24,
            position: 'absolute',
            right: 0,
            bottom: 30,
            marginHorizontal: 20,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <components.ProductInWishlist item={item} version={2} />
        </View>
      );
    };

    return (
      <View style={{marginBottom: 30}}>
        {renderImages()}
        {renderDots()}
        {renderInWishList()}
      </View>
    );
  };

  const renderNameWithRating = (): JSX.Element => {
    return <components.ProductNameInner item={item} />;
  };

  const renderPriceWithQuantity = (): JSX.Element => {
    return <components.ProductPriceInner item={item} />;
  };

  const renderDescription = (): JSX.Element => {
    const renderDescriptionHeader = () => {
      return (
        <text.T16
          style={{
            ...theme.fonts.H5,
            color: theme.colors.mainColor,
            marginBottom: 20,
          }}
        >
          Description
        </text.T16>
      );
    };

    const renderDescriptionText = () => {
      return (
        <View>
          <text.T16 style={{marginBottom: 14}} numberOfLines={5}>
            {item.description}
          </text.T16>
          <TouchableOpacity
            style={{flexDirection: 'row', alignItems: 'center'}}
            onPress={() =>
              navigation.navigate('Description', {
                description: item.description || '',
              })
            }
          >
            <text.T16
              style={{
                marginRight: 6,
                color: theme.colors.mainColor,
                textTransform: 'lowercase',
              }}
            >
              read more
            </text.T16>
            <svg.RightArrowSvg />
          </TouchableOpacity>
        </View>
      );
    };

    return (
      <View
        style={{
          paddingHorizontal: 20,
          marginBottom: 50,
        }}
      >
        {renderDescriptionHeader()}
        {renderDescriptionText()}
      </View>
    );
  };

  const renderReviews = (): JSX.Element | null => {
    if (reviews.length === 0 || !reviews) {
      return null;
    }
    const slice = reviews.slice(0, 2);
    const quantity = reviews.length;

    return (
      <View>
        <components.BlockHeading
          title={`Reviews (${quantity})`}
          onPress={() => navigation.navigate('Reviews')}
          containerStyle={{paddingHorizontal: 20}}
        />
        <View style={{paddingLeft: 20}}>
          {slice.map((item, index, array) => {
            return (
              <components.ReviewItem
                key={index}
                item={item}
                array={array}
                index={index}
              />
            );
          })}
        </View>
      </View>
    );
  };

  const renderButton = (): JSX.Element => {
    const renderAlert = () => {
      if (quantity > 0) {
        Alert.alert(
          'Item already in cart',
          'Do you want to add another one?',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'OK',
              onPress: () => {
                dispatch(fullRemoveFromCart(item));
                dispatch(
                  addToCart({
                    ...item,
                  }),
                );
                addedToCartMessage(item);
              },
            },
          ],
          {cancelable: false},
        );
        return;
      }
    };

    return (
      <components.Button
        title='+ ADd to cart'
        containerStyle={{
          paddingHorizontal: 20,
          marginTop: 20,
        }}
        onPress={() => {
          if (quantity > 0) {
            renderAlert();
            return;
          }
          dispatch(
            addToCart({
              ...item,
            }),
          );
          addedToCartMessage(item);
        }}
      />
    );
  };

  return (
    <components.SmartView>
      {renderCarousel()}
      {renderNameWithRating()}
      {renderPriceWithQuantity()}
      {renderDescription()}
      {renderReviews()}
      {renderButton()}
    </components.SmartView>
  );
};

export default _v1;
