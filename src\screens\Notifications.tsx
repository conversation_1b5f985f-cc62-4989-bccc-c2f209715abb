import React from 'react';
import {ScrollView, View, Text, Image} from 'react-native';

import {text} from '../text';
import {theme} from '../constants';
import {components} from '../components';
import {notifications} from '../constants';

const Notifications: React.FC = (): JSX.Element => {
  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Notifications' goBack={true} />;
  };

  const renderContent = () => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingTop: 25,
          paddingBottom: 25,
          paddingHorizontal: 20,
        }}
      >
        {notifications.map((item, index, array) => {
          const lastElement = index === array.length - 1;
          return (
            <components.Container
              key={index}
              onPressDisabled={false}
              activeOpacity={0.3}
              containerStyle={{
                marginBottom: lastElement ? 0 : 14,
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 14,
                }}
              >
                <item.icon />
                <text.T16
                  style={{
                    marginLeft: 8,
                  }}
                  numberOfLines={1}
                >
                  {item.title}
                </text.T16>
              </View>
              {item.image && (
                <Image
                  source={{uri: item.image}}
                  style={{
                    width: '100%',
                    aspectRatio: 2.26,
                    marginBottom: 14,
                    borderRadius: 5,
                    backgroundColor: theme.colors.imageBackground,
                  }}
                />
              )}
              {item.description && (
                <text.T14
                  style={{
                    marginBottom: 14,
                  }}
                >
                  {item.description}
                </text.T14>
              )}
              <components.Line />
              <Text
                style={{
                  ...theme.fonts.DMSans_400Regular,
                  fontSize: 12,
                  lineHeight: 12 * 1.5,
                  color: theme.colors.textColor,
                  marginTop: 14,
                }}
              >
                {item.date}
              </Text>
            </components.Container>
          );
        })}

        {/* {notifications.map((item, index, array) => {
          const lastElement = index === array.length - 1;

          return (
            <components.NotificationItem
              key={index}
              item={item}
              containerStyle={{
                marginBottom: lastElement ? 0 : 14,
              }}
            />
          );
        })} */}
      </ScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default Notifications;
