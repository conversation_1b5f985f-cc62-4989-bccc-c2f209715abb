import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../types';
import removeJWTTokens from './jwt/remove-tokens';

interface LogoutParams {
  navigation: NativeStackNavigationProp<RootStackParamList>;
  refreshUserAuthentication: () => void;
}

export async function logout({
  navigation,
  refreshUserAuthentication,
}: LogoutParams) {
  await removeJWTTokens();
  refreshUserAuthentication();
  navigation.navigate('SignIn');
}
