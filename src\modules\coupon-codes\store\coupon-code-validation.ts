import { create } from "zustand";
import { CouponCodeType } from "../types";

type Store = {
  validatedCouponCode: CouponCodeType | null;
  setValidatedCouponCode: (coupon: CouponCodeType | null) => void;
};

const useValidatedCouponCode = create<Store>((set) => ({
  validatedCouponCode: null,
  setValidatedCouponCode: (validatedCouponCode) =>
    set((store) => ({
      validatedCouponCode: validatedCouponCode,
    })),
}));

export default useValidatedCouponCode;
