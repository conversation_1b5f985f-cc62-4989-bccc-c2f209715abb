import Image from "next/image";
import { ProductItemType } from "../types/products";
import Text from "@/styles/text-styles";
import { useCartStore } from "../store/cart-store";
import { useEffect, useState } from "react";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { formatPrice } from "@/modules/catalog/utils/prices-transformation";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface Props {
  productItem: ProductItemType;
}

export default function SimilarProduct({ productItem }: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const { addProductItem } = useCartStore((store) => store.actions);
  const { currency } = useCurrency();

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  const handleAddToCart = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    addProductItem({
      slug: productItem.slug,
      id: productItem.id,
      productId: productItem.productId,
      cartQuantity: 1,
      name: productItem.name,
      prices: productItem.prices,
      image: productItem.image,
      variations: productItem.variations,
      inStock: productItem.inStock,
    });
  };

  return (
    <div className="flex flex-col items-center space-y-2 h-full shadow-sm rounded-xl">
      {/* Product Image */}
      <div className="w-full aspect-square relative bg-white rounded-lg overflow-hidden">
        <Image
          src={productImage}
          onError={() => setProductImage("/not-found/product-image.webp")}
          alt={productItem.name}
          fill
          unoptimized
          className="object-cover"
        />
        <Button
          onClick={handleAddToCart}
          className="absolute top-2 right-2 w-8 h-8 rounded-full bg-primary text-white hover:bg-primary hover:text-white p-0 shadow-md border border-primary"
          size="icon"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Product Name */}
      <div className="text-center px-1 flex-1 flex items-center">
        <Text
          textStyle="TS7"
          className="text-black font-medium line-clamp-2 leading-tight"
        >
          {productItem.name}
        </Text>
      </div>

      {/* Product Price */}
      <div className="text-center pb-1">
        <Text textStyle="TS5" className="text-primary font-bold">
          {formatPrice(productItem.prices[0].promotionalPrice)} {currency}
        </Text>
      </div>
    </div>
  );
}
