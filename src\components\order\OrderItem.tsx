import React from 'react';
import {View, Text, TouchableOpacity, ImageBackground} from 'react-native';

import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import {OrderItemType} from '../../modules/checkout/types/orders';
import useCurrency from '../../modules/catalog/hooks/use-currency';

type Props = {item: OrderItemType; lastElement?: boolean};

const OrderItem: React.FC<Props> = ({item, lastElement}): JSX.Element => {
  const navigation = useAppNavigation();
  const {currency} = useCurrency();

  const marginBottom = lastElement ? 30 : 14;

  const renderImage = () => {
    return (
      <TouchableOpacity>
        <ImageBackground
          source={{uri: item.image}}
          style={{width: 100, height: '100%'}}
          imageStyle={{
            borderTopLeftRadius: 5,
            borderBottomLeftRadius: 5,
            backgroundColor: theme.colors.imageBackground,
          }}
          resizeMode='contain'
        />
      </TouchableOpacity>
    );
  };

  const renderInfo = () => {
    return (
      <View
        style={{
          borderTopWidth: 1,
          borderBottomWidth: 1,
          borderColor: theme.colors.lightBlue,
          width: '100%',
          padding: 14,
          paddingRight: 0,
          flexDirection: 'row',
          flex: 1,
        }}
      >
        <View style={{marginRight: 'auto', justifyContent: 'space-between'}}>
          <Text
            style={{
              ...theme.fonts.textStyle_14,
              color: theme.colors.textColor,
              marginBottom: 3,
            }}
            numberOfLines={1}
          >
            {item.name}
          </Text>
          <Text
            style={{
              ...theme.fonts.textStyle_16,
              color: theme.colors.mainColor,
              marginBottom: 'auto',
            }}
          >
            {currency} {item.price.toFixed(2)}
          </Text>
          <Text
            style={{
              fontSize: 12,
              color: theme.colors.textColor,
            }}
          >
            Quantity: {item.quantity}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        width: '100%',
        height: 100,
        marginBottom: marginBottom,
      }}
    >
      {renderImage()}
      {renderInfo()}
    </View>
  );
};

export default OrderItem;
