import { z } from "zod";

const FrenchSignUpFormSchema = z.object({
  firstName: z.string().min(1, { message: "Le prénom est requis." }),
  lastName: z.string().min(1, { message: "Le nom de famille est requis." }),
  email: z
    .string()
    .min(1, { message: "L'adresse e-mail est requise." })
    .email("Format d'adresse e-mail invalide."),
  password: z.string().min(8, {
    message: "Le mot de passe doit contenir au moins 8 caractères.",
  }),
});

export function getSignUpFormSchema() {
  return FrenchSignUpFormSchema;
}
