type OrderStatus =
  | 'Processing'
  | 'Delivered'
  | 'Cancelled'
  | 'Returned'
  | 'Failed'
  | 'Refused'
  | 'AwaitingApproval';

export interface OrderResponseDataType {
  id: string;
  code: string;
  total: number;
  shippingCost: number;
  currency: string;
  status: OrderStatus;
  createdAt: string;
  notes: string | null;
  voucherCode: VoucherCodeInResponse | null;
  address: {
    email: string;
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string | null;
    company?: string | null;
    zone?: string | null;
    postalCode?: string | null;
    phone?: string;
    city?: {
      name: string;
      country: {
        name: string;
        isoCode: string;
      };
    };
  };
  items: OrderItemTypeInResponse[];
}

interface OrderItemTypeInResponse {
  name: string;
  quantity: number;
  price: number;
  image: string | null;
  barcode: string;
  voucherCode: VoucherCodeInResponse | null;
  promotion: PromotionInOrderResponse | null;
}

interface PromotionInOrderResponse {
  name: string;
  discount: {
    type: 'percentage' | 'amount';
    value: string;
  };
}

interface VoucherCodeInResponse {
  code: string;
  discount: {
    type: 'percentage' | 'amount';
    value: number;
  };
}

export interface OrderDataType {
  id: string;
  code: string;
  total: number;
  shippingCost: number;
  currency: string;
  status: OrderStatus;
  createdAt: string;
  notes: string | null;
  voucherCode: VoucherCode | null;
  address: {
    email: string;
    firstName: string;
    lastName: string;
    address1: string;
    address2: string | null;
    company: string | null;
    zone: string | null;
    postalCode: string | null;
    phone: string;
    city: {
      name: string;
      country: {
        name: string;
        isoCode: string;
      };
    };
  };
  discount: number;
  items: OrderItemType[];
}

export interface OrderItemType {
  name: string;
  quantity: number;
  price: number;
  promotionnalPrice: number;
  image: string;
  barcode: string;
  couponCode: VoucherCode | null;
  promotion: PromotionInOrder | null;
}

interface PromotionInOrder {
  name: string;
  discount: {
    type: 'percentage' | 'amount';
    value: number;
  };
}

interface VoucherCode {
  code: string;
  discount: {
    type: 'percentage' | 'amount';
    value: number;
  };
}

interface itemType {
  productItemId: string;
  quantity: number;
}

export interface RegisterGuestOrderRequestParams {
  items: itemType[];
  address: {[key: string]: string};
  voucherCode?: string;
  notes: string;
}

export interface RegisterUserOrderRequestParams {
  items: itemType[];
  address?: {[key: string]: string} | string;
  addressId?: string;
  voucherCode?: string;
  notes: string;
}
