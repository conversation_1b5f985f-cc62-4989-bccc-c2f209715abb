import {AxiosError} from 'axios';
import castToCartItem from '../utils/types-casting/cart-items';
import {ProductInResponse} from '../types/products';
import extractJWTokens from '../../auth/utils/jwt/extract-tokens';
import {refreshToken} from '../../auth/services/refresh-token';
import {CustomError} from '../../../lib/custom-error';
import {GET} from '../../../lib/http-methods';

export async function extractCartItems() {
  const {access} = await extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET('/carts/products', headers);
    const cartItems = (res.data as ProductInResponse[]).map((cartItem) =>
      castToCartItem(cartItem),
    );

    return cartItems || [];
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(extractCartItems);
      if (!res) throw new CustomError('Unauthorized', 401);

      return res;
    } else throw new CustomError('Server Error!', 500);
  }
}
