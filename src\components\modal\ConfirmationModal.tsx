import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {theme} from '../../constants';

interface Props {
  isVisible: boolean;
  title: string;
  description: string;
  confirmText: string;
  cancelText: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  warning?: string;
}

const ConfirmationModal: React.FC<Props> = ({
  isVisible,
  title,
  description,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  isLoading = false,
  warning,
}): JSX.Element => {
  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.content}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.description}>{description}</Text>
            
            {warning && (
              <Text style={styles.warning}>{warning}</Text>
            )}
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onCancel}
                disabled={isLoading}
              >
                <Text style={styles.cancelButtonText}>{cancelText}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.confirmButton,
                  isLoading && styles.disabledButton,
                ]}
                onPress={onConfirm}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color={theme.colors.white} size="small" />
                ) : (
                  <Text style={styles.confirmButtonText}>{confirmText}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: 15,
    padding: 20,
    width: '85%',
    maxWidth: 400,
  },
  content: {
    alignItems: 'center',
  },
  title: {
    ...theme.fonts.DMSans_700Bold,
    fontSize: 18,
    color: theme.colors.mainColor,
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 16,
    color: theme.colors.textColor,
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 22,
  },
  warning: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 14,
    color: '#E82837',
    marginBottom: 15,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 45,
  },
  cancelButton: {
    backgroundColor: theme.colors.lightBlue,
    borderWidth: 1,
    borderColor: theme.colors.lightBlue,
  },
  confirmButton: {
    backgroundColor: '#E82837',
  },
  disabledButton: {
    opacity: 0.7,
  },
  cancelButtonText: {
    ...theme.fonts.DMSans_500Medium,
    fontSize: 16,
    color: theme.colors.mainColor,
  },
  confirmButtonText: {
    ...theme.fonts.DMSans_500Medium,
    fontSize: 16,
    color: theme.colors.white,
  },
});

export default ConfirmationModal;
