import { create } from "zustand";
import { City } from "../types/addresses";
import { Country } from "@shopify/address";

interface CheckoutStore {
  country: Country | null;
  city: City;
  countryOptionnalLabels: string[];
  notes: string;
  setCity: (city: City) => void;
  setCountry: (country: Country) => void;
  setCountryOptionnalLabels: (countryOptionnalLabels: string[]) => void;
  setNotes: (notes: string) => void;
}

export const useCheckoutStore = create<CheckoutStore>((set, get) => ({
  country: null,
  city: { name: "", code: "" },
  countryOptionnalLabels: [],
  notes: "",
  setCity: (city) => set({ city }),
  setCountry: (country) => set({ country }),
  setNotes: (notes) => set({ notes }),
  setCountryOptionnalLabels: (countryOptionnalLabels) =>
    set({ countryOptionnalLabels }),
}));
