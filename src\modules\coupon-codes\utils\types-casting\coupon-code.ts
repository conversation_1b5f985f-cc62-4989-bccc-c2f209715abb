import { CouponCodeInResponseType, CouponCodeType } from "../../types";

export default function castToCouponCode(
  couponCode: CouponCodeInResponseType
): CouponCodeType {
  return {
    id: couponCode.id,
    description: couponCode.description,
    code: couponCode.code,
    allowOnPromotions: couponCode.allowOnPromotions,
    appliesToAllProducts: couponCode.appliesToAllProducts,
    freeShipping: couponCode.freeShipping,
    productItems: couponCode.productItems,
    unlimitedUsage: couponCode.unlimitedUsage,
    maxRedemptions: couponCode.maxRedemptions,
    maxUsesPerUser: couponCode.maxUsesPerUser,
    totalVoucherRedemptionsByUsers: couponCode.totalVoucherRedemptionsByUsers,
    discount: couponCode.discount,
    period: couponCode.period,
  };
}
