import React, {PropsWithChildren} from 'react';
import {View, Text, ViewStyle} from 'react-native';

import type {ProductType} from '../../modules/catalog/types/products';
import {ReviewType} from '../../types/ReviewType';

type Props = PropsWithChildren<{
  item: ProductType | ReviewType;
  version: number;
  containerStyle?: ViewStyle;
}>;

import {svg} from '../../assets/svg';
import {theme} from '../../constants';

const ProductRating: React.FC<Props> = ({
  item,
  version,
  containerStyle,
}): JSX.Element | null => {
  if (version === 1) {
    return (
      <View
        style={{
          ...containerStyle,
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <svg.RatingStarSvg />
        <Text
          style={{
            ...theme.fonts.DMSans_400Regular,
            fontSize: 12,
            lineHeight: 12 * 1.7,
            marginLeft: 4,
            color: theme.colors.textColor,
          }}
        >
          5.0
        </Text>
      </View>
    );
  }

  return null;
};

export default ProductRating;
