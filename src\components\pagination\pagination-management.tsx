import {
  PaginationEllipsis,
  PaginationItem,
  PaginationList,
  PaginationNext,
  PaginationPrev,
} from './pagination-list';
import {useEffect} from 'react';

interface Props {
  currentPage: number;
  pagesNumber: number;
  changePage: (page: number) => void;
  scrollToTop?: boolean;
}

export default function PaginationMangement({
  currentPage,
  pagesNumber,
  changePage,
  scrollToTop = true,
}: Props) {
  useEffect(() => {
    if (scrollToTop)
      window.scrollTo({
        top: 0,
        behavior: 'instant',
      });
  }, [currentPage]);

  return pagesNumber === 1 ? null : (
    <PaginationList>
      {/* displaying first item with ellipsis in big pages number */}
      {
        <PaginationPrev
          disabled={currentPage === 1}
          className={`pr-3 text-primary font-semibold ${
            currentPage === 1 && 'opacity-60'
          }`}
          onClick={() => changePage(currentPage - 1)}
        >
          Prev
        </PaginationPrev>
      }
      {pagesNumber >= 5 && currentPage > 2 ? (
        <PaginationItem onClick={() => changePage(1)}>01</PaginationItem>
      ) : null}
      {pagesNumber >= 5 && currentPage > 3 ? <PaginationEllipsis /> : null}
      {pagesNumber < 5
        ? Array.from({length: pagesNumber}).map((_, index) => (
            <PaginationItem
              key={index}
              isActive={index + 1 === currentPage}
              onClick={() => changePage(index + 1)}
            >
              {(index + 1).toString().padStart(2, '0')}
            </PaginationItem>
          ))
        : Array.from({length: 3}).map((_, index) => {
            //getting page before and after if the page over 1 or 2 afters if page is 1 or 2 before if page is total pages
            const pageNumber =
              currentPage > 1
                ? currentPage === pagesNumber
                  ? currentPage + index - 2 //2 pages before pages number
                  : currentPage + index - 1 //page before and after
                : currentPage + index; //two pages after
            return pageNumber <= pagesNumber ? (
              <PaginationItem
                key={index}
                isActive={pageNumber === currentPage}
                onClick={() => changePage(pageNumber)}
              >
                {pageNumber.toString().padStart(2, '0')}
              </PaginationItem>
            ) : null;
          })}
      {pagesNumber >= 5 ? (
        (currentPage !== 1 && currentPage + 2 < pagesNumber) ||
        (currentPage === 1 && currentPage + 2 < pagesNumber) ? (
          <PaginationEllipsis />
        ) : null
      ) : null}
      {pagesNumber >= 5 ? (
        (currentPage !== 1 && currentPage + 1 < pagesNumber) ||
        (currentPage === 1 && currentPage + 2 < pagesNumber) ? (
          <PaginationItem onClick={() => changePage(pagesNumber)}>
            {pagesNumber.toString().padStart(2, '0')}
          </PaginationItem>
        ) : null
      ) : null}
      {
        <PaginationNext
          disabled={currentPage === pagesNumber}
          className={`pr-3 text-primary font-semibold ${
            currentPage === pagesNumber && 'opacity-60'
          }`}
          onClick={() => changePage(currentPage + 1)}
        >
          Next
        </PaginationNext>
      }
    </PaginationList>
  );
}
