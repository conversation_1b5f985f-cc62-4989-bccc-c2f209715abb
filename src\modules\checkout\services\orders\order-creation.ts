import { POST } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { CustomError } from "../../../../utils/custom-error";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import {
  RegisterGuestOrderRequestParams,
  RegisterUserOrderRequestParams,
} from "../../types/orders";

export async function registerGuestOrderOnServerSide(
  orderData: RegisterGuestOrderRequestParams
) {
  try {
    const response = await POST("/orders/guest/register", {}, orderData);
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === "P9000") {
        throw new CustomError(
          "productItemOutOfStock",
          axiosError.response?.status,
          axiosError.response?.data.code
        );
      }
      throw new CustomError("Server Error!", 500);
    }
  }
}

export async function registerUserOrderOnServerSide(
  orderData: RegisterUserOrderRequestParams
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const response = await POST(
      "/orders/authenticated/register",
      headers,
      orderData
    );
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        registerUserOrderOnServerSide(orderData)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === "P9000") {
        throw new CustomError(
          "productItemOutOfStock",
          axiosError.response?.status,
          axiosError.response?.data.code
        );
      }
      throw new CustomError("Server Error!", 500);
    }
  }
}
