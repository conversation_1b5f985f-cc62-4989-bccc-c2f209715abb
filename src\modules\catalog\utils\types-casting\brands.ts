import {BrandInResponseType, BrandType} from '../../types/brands';

const CATALOG_BACKEND_URL = 'https://api-akal.tdg.tn';
const FALLBACK_IMAGE =
  'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image';

export function castToBrandType(
  brandInResponse: BrandInResponseType,
): BrandType {
  return {
    slug: brandInResponse.slug,
    id: brandInResponse.id,
    name: brandInResponse.name,
    description: brandInResponse.description,
    image: brandInResponse.image
      ? `${CATALOG_BACKEND_URL}${brandInResponse.image}`
      : FALLBACK_IMAGE,
    numberOfProducts: brandInResponse.numberOfProducts || 0,
  };
}
