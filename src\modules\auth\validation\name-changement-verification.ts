import {getNameChangementWarnings} from '../utils/warnings/password-changement/input-warning';
import {getNameChangementSchema} from './schemas/auth/name-changement';

type ResponseType = {
  ok: boolean;
  warning: {
    newName: string;
  };
};

export function verifyNameChangementData(name: string): ResponseType {
  const nameSchema = getNameChangementSchema();
  const verificationResult = nameSchema.safeParse({newName: name});

  if (!verificationResult.success) {
    return {
      ok: false,
      warning: getNameChangementWarnings(verificationResult),
    };
  }

  return {
    ok: true,
    warning: {
      newName: '',
    },
  };
}
