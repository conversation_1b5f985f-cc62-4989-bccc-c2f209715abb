import { keepPreviousData, useQuery } from "@tanstack/react-query";
import retrievePromotionsFromServerSide from "../../services/promotions/promotions-extraction";

interface Params {
  limit?: number;
}

export default function usePromotions({ limit }: Params) {
  const { data, isLoading } = useQuery({
    queryKey: ["promotions", limit],
    queryFn: () => retrievePromotionsFromServerSide({ limit }),
    placeholderData: keepPreviousData,
  });

  return {
    promotions: data,
    promotionsAreLoading: isLoading,
  };
}
