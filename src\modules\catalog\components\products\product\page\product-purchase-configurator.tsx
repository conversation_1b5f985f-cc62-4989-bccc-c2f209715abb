import {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {components} from '../../../../../../components';

interface Props {
  productItem: any | null;
}

export default function ProductPurchaseConfigurator({productItem}: Props) {
  const [quantity, setQuantity] = useState(1);
  const addProductItem = (item: any) => console.log('Add to cart:', item);
  const toggleProductItem = (item: any) =>
    console.log('Toggle wishlist:', item);
  const checkIsProductInWishlist = (id: string) => false;

  const isProductInWishlist = productItem
    ? checkIsProductInWishlist(productItem.id)
    : false;

  const handleWishlistToggle = () => {
    if (productItem) {
      toggleProductItem({
        slug: productItem.slug,
        productId: productItem.productId,
        id: `temp-${productItem.id}`,
        productItemId: productItem.id,
        name: productItem.name,
        image: productItem.image,
        prices: productItem.prices,
        variations: productItem.variations,
        inStock: productItem.inStock,
      });
    }
  };

  return productItem ? (
    <View style={styles.container}>
      {/* Quantity and Add to Cart */}
      <View style={styles.actionRow}>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            disabled={quantity <= 1}
            onPress={() => setQuantity(quantity - 1)}
            style={[styles.quantityButton, quantity <= 1 && styles.disabled]}
          >
            <Text style={styles.quantityButtonText}>-</Text>
          </TouchableOpacity>

          <Text style={styles.quantityText}>{quantity}</Text>

          <TouchableOpacity
            onPress={() => setQuantity(quantity + 1)}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>

        <components.Button
          title='Ajouter au panier'
          onPress={() => {
            addProductItem({
              slug: productItem.slug,
              id: productItem.id,
              productId: productItem.productId,
              cartQuantity: quantity,
              name: productItem.name,
              prices: productItem.prices,
              image: productItem.image,
              variations: productItem.variations,
              inStock: productItem.inStock,
            });
          }}
          containerStyle={styles.addToCartButton}
        />

        {/* Heart Favorite Button */}
        <TouchableOpacity
          onPress={handleWishlistToggle}
          style={styles.wishlistButton}
        >
          <Text style={styles.wishlistIcon}>
            {isProductInWishlist ? '♥' : '♡'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Buy Now Button */}
      <components.Button
        title='Acheter maintenant'
        onPress={() => {
          addProductItem({
            slug: productItem.slug,
            id: productItem.id,
            cartQuantity: quantity,
            productId: productItem.productId,
            name: productItem.name,
            prices: productItem.prices,
            image: productItem.image,
            variations: productItem.variations,
            inStock: productItem.inStock,
          });
          // Navigation would be handled by parent component
        }}
        containerStyle={styles.buyNowButton}
      />
    </View>
  ) : null;
}

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'stretch',
    gap: 12,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginHorizontal: 16,
    minWidth: 32,
    textAlign: 'center',
  },
  addToCartButton: {
    flex: 1,
    borderRadius: 20,
    paddingVertical: 16,
  },
  wishlistButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wishlistIcon: {
    fontSize: 20,
    color: '#FF4444',
  },
  buyNowButton: {
    borderRadius: 20,
    paddingVertical: 16,
  },
});
