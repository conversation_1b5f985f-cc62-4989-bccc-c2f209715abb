import {Alert} from 'react-native';

export interface WarningConfig {
  title?: string;
  message: string;
  showAlert?: boolean;
}

export class WarningManagerRN {
  static showWarning(config: WarningConfig) {
    if (config.showAlert !== false) {
      Alert.alert(config.title || 'Warning', config.message, [
        {text: 'OK', style: 'default'},
      ]);
    }
  }

  static showError(message: string, title?: string) {
    Alert.alert(title || 'Error', message, [{text: 'OK', style: 'default'}]);
  }

  static showSuccess(message: string, title?: string) {
    Alert.alert(title || 'Success', message, [{text: 'OK', style: 'default'}]);
  }

  static showConfirmation(
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    title?: string,
  ) {
    Alert.alert(title || 'Confirm', message, [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: onCancel,
      },
      {
        text: 'OK',
        style: 'default',
        onPress: onConfirm,
      },
    ]);
  }

  static showInvalidCredentials() {
    this.showError('Invalid email or password. Please try again.');
  }

  static showEmailAlreadyExists() {
    this.showError('An account with this email already exists.');
  }

  static showNetworkError() {
    this.showError(
      'Network error. Please check your connection and try again.',
    );
  }

  static showInvalidCode() {
    this.showError('Invalid verification code. Please try again.');
  }

  static showEmailNotFound() {
    this.showError('No account found with this email address.');
  }

  static showPasswordResetSuccess() {
    this.showSuccess(
      'Password reset successfully. You can now sign in with your new password.',
    );
  }

  static showSignUpSuccess() {
    this.showSuccess('Account created successfully. Please sign in.');
  }
}

// Hook for managing warnings in components
import {useState} from 'react';

export interface WarningState {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  generalWarning: string;
}

export function useWarningManager() {
  const [warnings, setWarnings] = useState<WarningState>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    generalWarning: '',
  });

  const clearWarnings = () => {
    setWarnings({
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      generalWarning: '',
    });
  };

  const setWarning = (field: keyof WarningState, message: string) => {
    setWarnings((prev) => ({
      ...prev,
      [field]: message,
    }));
  };

  const setAllWarnings = (newWarnings: Partial<WarningState>) => {
    setWarnings((prev) => ({
      ...prev,
      ...newWarnings,
    }));
  };

  const hasWarnings = () => {
    return Object.values(warnings).some((warning) => warning !== '');
  };

  const showGeneralWarning = () => {
    if (warnings.generalWarning) {
      WarningManagerRN.showWarning({
        message: warnings.generalWarning,
      });
    }
  };

  return {
    warnings,
    setWarning,
    setAllWarnings,
    clearWarnings,
    hasWarnings,
    showGeneralWarning,
  };
}
