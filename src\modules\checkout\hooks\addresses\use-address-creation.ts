import {FormEvent, useState} from 'react';
import verifyAddressContent from '../../utils/address-verification';
import {useCheckoutStore} from '../../store/checkout-store';
import createAddressOnServerSide from '../../services/addresses/address-creation';
import {useQueryClient} from '@tanstack/react-query';
import useAddressSelection from '../../store/address-selection-store';
import {useAppNavigation} from '../../../../hooks';

export default function useAddressCreation(useAddressIdInCheckout = false) {
  const queryClient = useQueryClient();
  const {country, city} = useCheckoutStore();
  const navigation = useAppNavigation();
  const [warning, setWarning] = useState('');
  const [wrongInputs, setWrongInputs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    address1: '',
    address2: '',
    company: '',
    province: 'REGION', // Need to check this later
    zip: '',
    phone: '',
    city: '',
    country: '',
    address: '',
    state: '',
    zipCode: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => {
      const newData = {...prev, [field]: value};

      if (field === 'address') {
        newData.address1 = value;
      } else if (field === 'state') {
        newData.province = value;
      } else if (field === 'zipCode') {
        newData.zip = value;
      }

      return newData;
    });
  };

  const {countryOptionnalLabels} = useCheckoutStore();

  const selectAddressForCheckout = useAddressSelection(
    (store) => store.selectAddress,
  );

  function extractAddressInfo(): Record<string, string> | null {
    if (!country) return null;

    const verificationResult = verifyAddressContent(
      formData,
      countryOptionnalLabels,
      country,
      city,
    );

    if (!verificationResult.valid) {
      setWrongInputs(verificationResult.wrongInputsFinded);
      setWarning(verificationResult.warning);
      return null;
    } else {
      if (warning !== '') setWarning('');
      if (wrongInputs.length > 0) setWrongInputs([]);
    }

    return verificationResult.addressData;
  }

  async function createAddress(event?: FormEvent) {
    if (event) event.preventDefault();

    const addressData = extractAddressInfo();
    if (addressData) {
      setIsLoading(true);

      if (warning !== '') setWarning('');

      try {
        const res = await createAddressOnServerSide(addressData);
        let addressId = '';

        if (res.ok) {
          queryClient.invalidateQueries({
            queryKey: ['user-address'],
            exact: false,
          });

          if (res.address && useAddressIdInCheckout) {
            addressId = res.address.id;
          }

          if (formData)
            setFormData({
              email: '',
              firstName: '',
              lastName: '',
              address1: '',
              address2: '',
              company: '',
              province: '',
              zip: '',
              phone: '',
              city: '',
              country: '',
              // Legacy fields for backward compatibility
              address: '',
              state: '',
              zipCode: '',
            });

          navigation.navigate('MyAddress');
        } else {
          if (res.error === 'emailInvalid') setWarning('Invalid Email Format');
          else setWarning('Error while creating address');
        }

        setIsLoading(false);

        //returning address id to use in the chekout and select in case of checkout error
        if (useAddressIdInCheckout && addressId !== '') {
          selectAddressForCheckout(addressId);
          return addressId;
        }
      } catch {
        return '';
      }
    }

    return '';
  }

  return {
    extractAddressInfo,
    wrongInputs,
    warning,
    createAddress,
    isLoading,
    formData,
    handleInputChange,
  };
}
