import {GET} from '../../../../lib/http-methods';
import {castToCategoryType} from '../../utils/types-casting/categories';

interface Params {
  categorySlug: string;
}

export async function retrieveCategoryFromServerSide({categorySlug}: Params) {
  try {
    const res = await GET(`/categories/${categorySlug}`, {});

    return castToCategoryType(res.data);
  } catch (error) {
    return null;
  }
}
