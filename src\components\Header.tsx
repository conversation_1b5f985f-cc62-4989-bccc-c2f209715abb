import React, {PropsWithChildren, useState} from 'react';
import {useRoute} from '@react-navigation/native';
import {View, Text, TouchableOpacity, ViewStyle, Alert} from 'react-native';

type Props = PropsWithChildren<{
  skip?: boolean;
  title?: string;
  basket?: boolean;
  goBack?: boolean;
  filter?: boolean;
  search?: boolean;
  style?: ViewStyle;
  burgerIcon?: boolean;
  bottomLine?: boolean;
  skipOnPress?: () => void;
}>;

import {text} from '../text';
import {svg} from '../assets/svg';
import {theme} from '../constants';
import {setScreen} from '../store/slices/tabSlice';
import BurgerMenuModal from '../components/modal/BurgerMenuModal';
import {useAppNavigation, useAppSelector, useAppDispatch} from '../hooks';

const Header: React.FC<Props> = ({
  skip,
  title,
  style,
  basket,
  search,
  goBack,
  filter,
  burgerIcon,
  bottomLine,
  skipOnPress,
}) => {
  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();

  const [showModal, setShowModal] = useState(false);

  const route = useRoute();

  const cart = useAppSelector((state) => state.cartSlice.list);
  const total = useAppSelector((state) => state.cartSlice.total).toFixed(2);

  const handleOnPress = () => {
    if (cart.length > 0) {
      dispatch(setScreen('Order'));
      route.name === 'Shop' && navigation.navigate('TabNavigator');
      route.name === 'Product' && navigation.navigate('TabNavigator');
    }
    if (cart.length === 0) {
      Alert.alert('Your cart is empty', 'Please add some items to your cart', [
        {
          text: 'OK',
          onPress: () => console.log('OK Pressed'),
        },
      ]);
    }
  };

  const renderGoBack = (): JSX.Element | null => {
    if (goBack && navigation.canGoBack()) {
      return (
        <View style={{position: 'absolute', left: 0}}>
          <TouchableOpacity
            style={{
              paddingVertical: 12,
              paddingHorizontal: 20,
            }}
            onPress={() => navigation.goBack()}
          >
            <svg.GoBackSvg />
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  };

  const renderSkipText = (): JSX.Element | null => {
    if (skip) {
      return (
        <TouchableOpacity
          style={{
            right: 0,
            position: 'absolute',
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
          }}
          onPress={skipOnPress}
        >
          <Text
            style={{
              // ...theme.fonts.Lato_700Bold,
              fontSize: 14,
              lineHeight: 14 * 1.7,
            }}
          >
            Skip
          </Text>
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderTitle = (): JSX.Element | null => {
    if (title) {
      return (
        <Text
          style={{
            ...theme.fonts.textStyle_16,
            color: theme.colors.mainColor,
            textTransform: 'capitalize',
          }}
          numberOfLines={1}
        >
          {title}
        </Text>
      );
    }

    return null;
  };

  const renderSearch = (): JSX.Element | null => {
    if (search) {
      return (
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            width: theme.sizes.width - 190,
            marginRight: 60,
          }}
          onPress={() => navigation.navigate('Search')}
        >
          <View style={{marginRight: 7}}>
            <svg.SearchSvg />
          </View>
          <text.T14>search</text.T14>
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderFilter = (): JSX.Element | null => {
    if (filter) {
      return (
        <View style={{position: 'absolute', right: 0}}>
          <TouchableOpacity
            style={{
              paddingVertical: 12,
              paddingHorizontal: 20,
            }}
            onPress={() => navigation.navigate('Filter')}
          >
            {/* <svg.FilterSvg /> */}
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  };

  const renderBurger = (): JSX.Element | null => {
    if (burgerIcon) {
      return (
        <TouchableOpacity
          style={{
            position: 'absolute',
            left: 0,
            height: 42,
            paddingHorizontal: 20,
            flexDirection: 'row',
            alignItems: 'center',
          }}
          onPress={() => setShowModal(true)}
        >
          <svg.BurgerSvg />
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderBasket = (): JSX.Element | null => {
    if (basket) {
      return (
        <TouchableOpacity
          onPress={handleOnPress}
          style={{
            right: 0,
            position: 'absolute',
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
          }}
        >
          <View
            style={{
              height: 22,
              borderRadius: 11,
              paddingHorizontal: 7,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme.colors.mainColor,
            }}
          >
            <Text
              style={{
                color: theme.colors.white,
                ...theme.fonts.DMSans_700Bold,
                fontSize: 10,
              }}
            >
              {cart.length > 0 ? `$${total}` : '$0'}
            </Text>
          </View>
          <svg.BasketSvg />
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderBurgerMenu = () => {
    return (
      <BurgerMenuModal showModal={showModal} setShowModal={setShowModal} />
    );
  };

  const containerStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 42,
    borderBottomColor: bottomLine ? '#DBE9F5' : 'transparent',
    borderBottomWidth: bottomLine ? 1 : 0,
    ...style,
  };

  return (
    <View style={{...containerStyle}}>
      {renderGoBack()}
      {renderTitle()}
      {renderSkipText()}
      {renderFilter()}
      {renderSearch()}
      {renderBurger()}
      {renderBasket()}
      {renderBurgerMenu()}
    </View>
  );
};

export default Header;
