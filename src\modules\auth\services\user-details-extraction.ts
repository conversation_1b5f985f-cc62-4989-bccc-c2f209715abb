import {AxiosError} from 'axios';
import extractJWTokens from '../utils/jwt/extract-tokens';
import {GET} from '../../../lib/http-methods';
import {UserDataType, UserResponseDataType} from '../types';
import {castToUserType} from '../utils/data-utils/types-casting/user';
import {refreshToken} from './refresh-token';

export async function retrieveUserDetails(): Promise<UserDataType | null> {
  const {access} = await extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };
  try {
    const res = await GET(`/users/me`, header);
    const userData = res.data as UserResponseDataType;

    return castToUserType(userData);
  } catch (error) {
    const axiosError = error as AxiosError;
    console.log(axiosError);
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveUserDetails);

      return res;
    }

    return null;
  }
}
