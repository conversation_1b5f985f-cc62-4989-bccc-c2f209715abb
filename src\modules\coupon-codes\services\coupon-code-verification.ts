import {GET} from '@/lib/http-methods';
import {AxiosError} from 'axios';
import {CustomError} from '../../../utils/custom-error';
import castToCouponCode from '../utils/types-casting/coupon-code';
import {CouponCodeInResponseType, CouponCodeType} from '../types';
import extractJWTokens from '@/modules/auth/utils/jwt/extract-tokens';
import useUserStore from '@/modules/auth/store/user-store';

export async function verifyCouponCodeOnServerSide(
  couponCode: string,
): Promise<CouponCodeType | null> {
  try {
    const {access} = extractJWTokens();
    const headers = useUserStore.getState().user?.isAuthenticated
      ? {
          Authorization: `Bearer ${access}`,
        }
      : {};

    const res = await GET(`/voucher-codes/verify/${couponCode}`, headers);

    return res.data
      ? castToCouponCode(res.data as CouponCodeInResponseType)
      : null;
  } catch (error) {
    const axiosError = error as AxiosError<{code: string; message: string}>;

    if (axiosError.response?.status === 404)
      throw new CustomError(
        'notFound',
        axiosError.response?.status,
        axiosError.response?.data.code,
      );
    else if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === 'P9000') {
        throw new CustomError(
          'expiredCoupon',
          axiosError.response?.status,
          axiosError.response?.data.code,
        );
      } else if (axiosError.response?.data.code === 'P9001') {
        throw new CustomError(
          'usersLimitPassed',
          axiosError.response?.status,
          axiosError.response?.data.code,
        );
      }

      throw new CustomError('Server Error!', 500);
    }

    return null;
  }
}
