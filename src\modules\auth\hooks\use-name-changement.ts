import {FormEvent, useState} from 'react';
import {changeName} from '../services/name-changement';
import {verifyNameChangementData} from '../validation/name-changement-verification';
import {getPasswordChangementWarning} from '../utils/warnings/server-response-warning';
import {useQueryClient} from '@tanstack/react-query';
import {useAppNavigation} from '../../../hooks';
import {getNameChangementGeneralWarning} from '../utils/warnings/general-warning';

export default function useNameChangement() {
  const [isLoading, setIsLoading] = useState(false);
  const [nameChanged, setNameChanged] = useState(false);
  const [newName, setNewName] = useState('');
  const navigation = useAppNavigation();
  const queryClient = useQueryClient();

  const [warning, setWarning] = useState({
    newName: '',
    getGeneralWarning: '',
  });

  function submitName(event?: FormEvent) {
    if (event) event.preventDefault();

    const name = newName;

    const nameVerification = verifyNameChangementData(name);

    if (!nameVerification.ok) {
      setWarning({
        ...nameVerification.warning,
        getGeneralWarning: getNameChangementGeneralWarning(name),
      });
    } else {
      setIsLoading(true);

      // Clearing all warnings
      setWarning({
        newName: '',
        getGeneralWarning: '',
      });

      // Submit verified name change request
      changeName({
        name: name,
      })
        .then((res) => {
          setIsLoading(false);

          if (res?.ok) {
            setNameChanged(true);

            setTimeout(() => {
              setNameChanged(false);
            }, 4000);

            queryClient.invalidateQueries({queryKey: ['user-data']});
          } else {
            const warning = getPasswordChangementWarning(
              res.status,
              res.code as string,
            );
            setWarning({
              newName: '',
              getGeneralWarning: warning?.generalWarning as string,
            });
          }
        })
        .catch(() => {
          navigation.navigate('TabNavigator');
        });
    }
  }

  return {
    isLoading,
    submitName,
    newName,
    setNewName,
    warning,
    nameChanged,
  };
}
