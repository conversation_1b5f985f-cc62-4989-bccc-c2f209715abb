"use client";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import Box from "@assets/icons/box";
import Globe from "@assets/icons/globe";
import TalkingMenIcon from "@assets/icons/talking-men";
import SettingsIcon from "@assets/icons/settings";
import ShippingIcon from "@assets/icons/shipping";
import Image from "next/image";

export default function AuthServicesSection() {
  const t = useTranslations("shared.auth.services");

  const services = [
    {
      icon: <Box />,
      title: t("fastDelivery.title"),
      description: t("fastDelivery.description"),
    },
    {
      icon: <Globe />,
      title: t("internationalBrands.title"),
      description: t("internationalBrands.description"),
    },
    {
      icon: <ShippingIcon />,
      title: t("freeDelivery.title"),
      description: t("freeDelivery.description"),
    },
    {
      icon: <TalkingMenIcon />,
      title: t("afterSalesSupport.title"),
      description: t("afterSalesSupport.description"),
    },
    {
      icon: <SettingsIcon />,
      title: t("repairMaintenance.title"),
      description: t("repairMaintenance.description"),
    },
  ];

  return (
    <div className="bg-gradient-to-tr from-blue to-primary text-white XL:p-32 p-10 flex flex-col justify-center min-h-[85vh]">
      <div className="max-w-lg mx-auto space-y-10">
        <Image
          width={180}
          height={56}
          alt="Akal logo"
          src={"/logos/company-white-logo.svg"}
          priority
        />
        <div className="text-start">
          <p>
            <Text textStyle="TS5" className="text-white/90  px-4">
              {t("mainDescription")}
            </Text>
          </p>
        </div>

        <div className="space-y-6">
          {services.map((service, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className="flex-shrink-0 mt-1">{service.icon}</div>
              <div className="space-y-1">
                <Text
                  textStyle="TS6"
                  className="text-white font-semibold text-base"
                >
                  {service.title}
                </Text>
                <p>
                  <Text
                    textStyle="TS7"
                    className="text-white/85 text-sm leading-relaxed"
                  >
                    {service.description}
                  </Text>
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
