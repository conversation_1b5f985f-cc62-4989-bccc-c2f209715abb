import { useState, useRef } from 'react';
import { ScrollView } from 'react-native';

export default function usePaginationRN() {
  const [page, setPage] = useState(1);
  const [pagesNumber, setPagesNumber] = useState(1);
  const paginatedListRef = useRef<ScrollView>(null);

  const scrollToTop = () => {
    if (paginatedListRef.current) {
      paginatedListRef.current.scrollTo({ y: 0, animated: true });
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    scrollToTop();
  };

  return {
    page,
    setPage: handlePageChange,
    pagesNumber,
    setPagesNumber,
    paginatedListRef,
    scrollToTop,
  };
}
