import { Checkbox } from "@/components/ui/checkbox";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import { CriteriaType } from "@/modules/catalog/types";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import FilterChoiceContainer from "./filter-choice-container";

type SortingCriteriaType = {
  label:
    | "priceLowToHigh"
    | "priceHighToLow"
    | "newestFirst"
    | "oldestFirst"
    | "recommended"
    | "nameAToZ"
    | "nameZToA"
    | "mostSold";
  value: CriteriaType;
}[];

export const SortingCriteria: SortingCriteriaType = [
  { label: "recommended", value: "displayOrder" }, // For curated default sort
  { label: "mostSold", value: "mostSold" }, // Based on actual sales data
  { label: "priceLowToHigh", value: "priceAsc" },
  { label: "priceHighToLow", value: "priceDesc" },
  { label: "newestFirst", value: "createdAtDesc" },
  { label: "oldestFirst", value: "createdAtAsc" },
  { label: "nameAToZ", value: "nameAsc" },
  { label: "nameZToA", value: "nameDesc" },
];

export default function SoritingCriteriaDropDown() {
  const t = useTranslations("filtersPage");
  const { criteria: selectedCriteria, setCriteria } =
    useProductsFilteringStore();

  const handleClick = (criteria: CriteriaType) => {
    setCriteria(criteria);
  };

  return (
    <FilterChoiceContainer title={t.raw("sortDropdownPlaceholder")}>
      {SortingCriteria.map((criteria, index) => (
        <div key={index} className="flex items-center space-x-3">
          <Checkbox
            id={criteria.value}
            className="w-4 h-4"
            checked={criteria.value === selectedCriteria}
            onCheckedChange={() => handleClick(criteria.value)}
          />
          <label
            htmlFor={criteria.value}
            className="text-sm text-gray cursor-pointer"
          >
            <Text textStyle="TS7">
              {t("sortingCriteria." + criteria.label)}
            </Text>
          </label>
        </div>
      ))}
    </FilterChoiceContainer>
  );
}
