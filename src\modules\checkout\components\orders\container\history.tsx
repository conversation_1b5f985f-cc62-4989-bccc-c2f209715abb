"use client";
import Text from "@/styles/text-styles";
import { OrderDataType } from "../../../types/orders";
import { useTranslations } from "next-intl";
import HistoryOrderItemContainer from "../history-order-item-container";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";

type Props = {
  details: OrderDataType;
};

const OrderHistoryContainer = ({ details }: Props) => {
  const t = useTranslations("ordersManagement");
  const { currency } = useCurrency();

  return (
    <div className="L:min-w-[400px] XL:min-w-[600px] w-full space-y-4  h-fit flex flex-col bg-primary/50 XL:rounded-[33px] L:rounded-[25px] rounded-[18px] text-primary  border border-primary justify-between">
      <div className="flex justify-between bg-primary text-white XL:p-5 L:p-4 p-3 xl:rounded-tl-[30px] xl:rounded-tr-[30px] l:rounded-tl-[25px] l:rounded-tr-[25px] rounded-tl-[15px] rounded-tr-[15px]">
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS4" className="block">
            {t("order.labels.number")}
          </Text>
          <Text textStyle="TS4" className="font-bold block">
            #{details.code}
          </Text>
        </div>
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS4" className=" block">
            {t("order.labels.total")}
          </Text>
          <Text textStyle="TS4" className="font-bold block">
            {`${(
              details.total +
              details.shippingCost -
              details.discount
            ).toFixed(3)} ${currency}`}
          </Text>
        </div>
      </div>
      <div className="XL:p-6 L:p-5 p-4 flex flex-col space-y-7">
        {details.items.map((item, idx) => (
          <HistoryOrderItemContainer key={idx} item={item} />
        ))}

        <div className="flex flex-col space-y-3">
          <div className="flex justify-between">
            <Text textStyle="TS5" className="font-bold text-black">
              {details.discount !== 0
                ? t("order.labels.subTotalWithoutDiscount")
                : t("order.labels.subTotal")}
              :
            </Text>
            <Text textStyle="TS6">{`${details.total.toFixed(
              3
            )} ${currency}`}</Text>
          </div>

          {details.discount !== 0 && (
            <div className="flex justify-between">
              <Text textStyle="TS5" className="font-bold text-black">
                {t("order.labels.discount")}:
              </Text>
              <Text textStyle="TS6">{`${details.discount.toFixed(
                3
              )} ${currency}`}</Text>
            </div>
          )}

          <div className="flex justify-between">
            <Text textStyle="TS5" className="font-bold text-black">
              {t("order.labels.shippingCost")}:
            </Text>
            <Text textStyle="TS6">{`${details.shippingCost.toFixed(
              3
            )} ${currency}`}</Text>
          </div>

          <Separator />

          <div className="flex justify-between">
            <Text textStyle="TS5" className="font-bold text-black">
              {details.discount !== 0
                ? t("order.labels.subTotalWithDiscount")
                : t("order.labels.total")}
              :
            </Text>
            <Text textStyle="TS6">
              {`${(
                details.total +
                details.shippingCost -
                details.discount
              ).toFixed(3)} ${currency}`}
            </Text>
          </div>
        </div>

        <div className="space-y-2">
          {
            <div className="flex flex-wrap items-center gap-1">
              <span className="block md:inline">
                {t.rich("order.labels.ProcessingText")}
              </span>
              <Text
                textStyle="TS6"
                className={cn(
                  "font-normal text-secondary bg-orange-light border border-secondary rounded-3xl block py-1 px-3 w-fit md:py-1 md:px-3",
                  {
                    "bg-[#FFF3E4] text-[#8F4917] border-[#8F4917]":
                      "Returned" == details.status,
                    "text-primary bg-light-primary border-primary":
                      details.status === "Delivered",
                    "bg-[#F7F9FC] text-gray border-gray":
                      details.status === "Cancelled",
                    "bg-[#FFCBCB] text-[#F90000] border-[#F90000]": [
                      "Failed",
                      "Refused",
                    ].includes(details.status),
                  }
                )}
              >
                {details.status === "AwaitingApproval"
                  ? t("orderStatus.AwaitingApproval")
                  : details.status === "Refused"
                  ? t("orderStatus.Refused")
                  : details.status === "Failed"
                  ? t("orderStatus.Failed")
                  : details.status === "Cancelled"
                  ? t("orderStatus.Cancelled")
                  : details.status === "Delivered"
                  ? t("orderStatus.Delivered")
                  : details.status === "Returned"
                  ? t("orderStatus.Returned")
                  : details.status === "Processing"
                  ? t("orderStatus.Processing")
                  : null}
              </Text>
            </div>
          }
        </div>
      </div>
    </div>
  );
};

export default OrderHistoryContainer;
