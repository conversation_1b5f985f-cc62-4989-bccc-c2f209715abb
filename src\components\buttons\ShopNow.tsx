import React from 'react';
import type {PropsWithChildren} from 'react';
import {Text, TouchableOpacity} from 'react-native';

import {theme} from '../../constants';

type Props = PropsWithChildren<{
  containerStyle?: object;
  onPress?: () => void;
  text?: string;
}>;

const ShopNow: React.FC<Props> = ({
  containerStyle,
  onPress,
  text,
}): JSX.Element => {
  return (
    <TouchableOpacity
      style={{
        alignSelf: 'flex-start',
        borderTopWidth: 1,
        borderBottomWidth: 1,
        borderColor: theme.colors.mainColor,
        paddingVertical: 4,
        ...containerStyle,
      }}
      onPress={onPress}
      disabled={!onPress}
    >
      <Text
        style={{
          textTransform: 'uppercase',
          ...theme.fonts.DMSans_700Bold,
          fontSize: 12,
          lineHeight: 12 * 1.7,
        }}
      >
        {text}
      </Text>
    </TouchableOpacity>
  );
};

export default ShopNow;
