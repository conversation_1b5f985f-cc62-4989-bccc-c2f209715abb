import * as React from 'react';
import Svg, {Circle, Path, G, Defs, ClipPath} from 'react-native-svg';

import {theme} from '../../../constants/theme';
import {useAppSelector} from '../../../hooks';

const UserTabSvg: React.FC = (): JSX.Element => {
  const currentTabScreen = useAppSelector((state) => state.tab.screen);

  const bgColor =
    currentTabScreen === 'Profile'
      ? theme.colors.white
      : theme.colors.transparent;
  const iconColor =
    currentTabScreen === 'Profile'
      ? theme.colors.mainColor
      : theme.colors.white;

  return (
    <Svg width={50} height={58} fill='none'>
      <Circle cx={25} cy={33} r={25} fill={bgColor} />
      <Path
        d='M32.925 9.3h-15.85l2.878-1.059A4.251 4.251 0 0 0 18.485 0H15h20-3.484a4.251 4.251 0 0 0-1.468 8.241L32.925 9.3Z'
        fill={bgColor}
      />
      <G clipPath='url(#a)'>
        <Path
          d='M33.485 24.515A11.921 11.921 0 0 0 25 21a11.921 11.921 0 0 0-8.485 3.515A11.921 11.921 0 0 0 13 33c0 3.205 1.248 6.219 3.515 8.485A11.921 11.921 0 0 0 25 45c3.205 0 6.219-1.248 8.485-3.515A11.921 11.921 0 0 0 37 33c0-3.205-1.248-6.219-3.515-8.485Zm-14.47 17.222A6.059 6.059 0 0 1 25 36.716a6.059 6.059 0 0 1 5.984 5.02A10.534 10.534 0 0 1 25 43.595c-2.219 0-4.28-.687-5.984-1.857Zm2.17-10.243A3.82 3.82 0 0 1 25 27.678a3.82 3.82 0 0 1 3.816 3.816A3.82 3.82 0 0 1 25 35.309a3.82 3.82 0 0 1-3.816-3.815Zm11.017 9.267a7.474 7.474 0 0 0-2.184-3.519 7.476 7.476 0 0 0-2.173-1.372 5.221 5.221 0 0 0 2.377-4.376A5.228 5.228 0 0 0 25 26.272a5.228 5.228 0 0 0-5.222 5.222c0 1.83.947 3.444 2.377 4.376a7.478 7.478 0 0 0-2.173 1.372 7.476 7.476 0 0 0-2.184 3.52A10.567 10.567 0 0 1 14.406 33c0-5.841 4.753-10.594 10.594-10.594 5.841 0 10.594 4.753 10.594 10.594 0 3.063-1.307 5.825-3.392 7.761Z'
          fill={iconColor}
        />
      </G>
      <Defs>
        <ClipPath id='a'>
          <Path fill={bgColor} transform='translate(13 21)' d='M0 0h24v24H0z' />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default UserTabSvg;
