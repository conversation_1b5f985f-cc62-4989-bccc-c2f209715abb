import {GET} from '../../../../lib/http-methods';
import {CategoryInResponseType} from '../../types/categories';
import {castToCategoryType} from '../../utils/types-casting/categories';

export async function retrieveCategoriesFromServerSide() {
  try {
    const res = await GET(`/categories`, {});

    return (res.data as CategoryInResponseType[]).map((categoryInResponse) =>
      castToCategoryType(categoryInResponse),
    );
  } catch (error) {
    return null;
  }
}
