import React from 'react';
import {View, ScrollView} from 'react-native';

import {text} from '../text';
import {svg} from '../assets/svg';
import {useAppDispatch} from '../hooks';
import {components} from '../components';
import type {RootStackParamList} from '../types';
import {setScreen} from '../store/slices/tabSlice';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'OrderFailed'>;

const OrderFailed: React.FC<Props> = ({navigation}): JSX.Element => {
  const dispatch = useAppDispatch();

  const renderStatusBar = (): JSX.Element => {
    return <components.StatusBar />;
  };

  const renderContent: () => JSX.Element = () => {
    const scrollViewStyle: object = {
      flexGrow: 1,
      paddingHorizontal: 20,
      justifyContent: 'center',
    };

    return (
      <ScrollView
        contentContainerStyle={{...scrollViewStyle}}
        showsVerticalScrollIndicator={false}
      >
        <svg.FailedSvg />
        <text.H2
          style={{
            marginTop: 30,
            marginBottom: 14,
          }}
        >
          Sorry! Your order{'\n'}has failed!
        </text.H2>
        <text.T16>
          Something went wrong. Please try again to contimue your order.
        </text.T16>
      </ScrollView>
    );
  };

  const renderFooter: () => JSX.Element = () => {
    return (
      <View style={{padding: 20}}>
        <components.Button
          title='try again'
          onPress={() => console.log('Try again')}
          containerStyle={{marginBottom: 14}}
        />
        <components.Button
          title='go to my profile'
          onPress={() => {
            dispatch(setScreen('Profile'));
            navigation.navigate('TabNavigator');
          }}
          transparent={true}
        />
      </View>
    );
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderContent()}
      {renderFooter()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default OrderFailed;
