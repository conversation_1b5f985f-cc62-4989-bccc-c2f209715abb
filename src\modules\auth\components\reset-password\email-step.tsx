import WarnInput from "@/components/input/warn-input";
import { useResetPasswordContext } from "@/modules/auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function EmailStep() {
  const t = useTranslations("shared.reset-password");
  const { warning, submitEmail, email, setEmail, isLoading } =
    useResetPasswordContext();

  return (
    <div className="flex flex-col items-start justify-center space-y-1 text-primary ">
      <Text textStyle="TS4" className="font-bold text-black">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS7" className="w-full text-danger">
        {warning.generalWarning}
      </Text>
      <div className="w-full pt-6 flex flex-col space-y-6">
        <div className="space-y-2">
          <label>
            <Text textStyle="TS6" className="text-gray font-tajawal">
              {t.raw("emailStep.email")}
            </Text>
          </label>
          <WarnInput
            id="email"
            className="h-[56px] text-base "
            warning={warning.email}
            value={email}
            onChange={(event: any) => setEmail(event.target.value)}
          />
        </div>
        <Button
          className={cn(
            "w-full h-[56px] bg-primary text-white rounded-lg text-base font-semibold",
            { "opacity-70": isLoading }
          )}
          onClick={submitEmail}
          disabled={isLoading}
        >
          <Text textStyle="TS7">{t.raw("emailStep.button")}</Text>
        </Button>

        {/* Bottom redirect text */}
        <div className="w-full text-center space-y-3 pt-4">
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.haveAccount", {
                loginLink: (chunk) => (
                  <Link
                    href="/auth/sign-in"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.noAccount", {
                signupLink: (chunk) => (
                  <Link
                    href="/auth/sign-up"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
