'use client';

import {QueryClientProvider, QueryClient} from '@tanstack/react-query';

// Separate query client for catalog module to maintain isolation
export const catalogQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: 600000, // refresh data every 10 min
      refetchOnWindowFocus: false, // disable for React Native
      retry: 2,
      staleTime: 300000, // 5 minutes
    },
  },
});

// Main app query client (if needed for other parts of the app)
const appQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: 600000, //refresh data every 10 min
      refetchOnWindowFocus: true,
    },
  },
});

export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <QueryClientProvider client={catalogQueryClient}>
      {children}
    </QueryClientProvider>
  );
}
