import { useCartStore } from "@/modules/cart/store/cart-store";
import useValidatedCouponCode from "../store/coupon-code-validation";
import {
  calculateDetailedCouponBreakdown,
  getTotalDiscountFromBreakdown,
} from "../utils/detailed-coupon-breakdown";

export default function useDetailedCouponBreakdown() {
  const { cartItems } = useCartStore((store) => store.state);
  const validatedCouponCode = useValidatedCouponCode(
    (store) => store.validatedCouponCode
  );
  const productDiscounts = calculateDetailedCouponBreakdown(
    validatedCouponCode,
    cartItems
  );

  const totalDiscount = getTotalDiscountFromBreakdown(productDiscounts);
  const hasAnyDiscounts = totalDiscount > 0;

  return {
    productDiscounts,
    totalDiscount,
    hasAnyDiscounts,
  };
}
