import {Dispatch, SetStateAction} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {useProductsFilteringStore} from '../../../store/products-filter';
import {components} from '../../../../components';
import {theme} from '../../../../constants';
import SidebarSkeletons from './sidebar-skeletons';
import CategoriesSelectionDropDown from './categories-selection';
import BrandsSelectionDropDown from './brands-selection-dropdown';
import PriceRangeDropdown from './price-range-dropdown';
import SoritingCriteriaDropDown from './sorting-criteria-dropdown';
import VariationsSelectionDropDown from './variations-selection-dropdown';

interface Props {
  setFilterIsOpen: Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
  filterHeaderIsUsed?: boolean;
  criteriaDropDownIsUsed?: boolean;
}

export default function FilterOptions({
  setFilterIsOpen,
  isLoading = false,
  filterHeaderIsUsed = false,
  criteriaDropDownIsUsed = true,
}: Props) {
  const doubleExtraL = 900;
  const {width} = Dimensions.get('window');
  const {joinedPageParam, categories} = useProductsFilteringStore();

  return (
    <View style={styles.container}>
      {/* Header Section */}
      {filterHeaderIsUsed && (
        <>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Filtres</Text>
            <TouchableOpacity
              onPress={() => setFilterIsOpen(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.separator} />
        </>
      )}

      {/* Content Section */}
      {!isLoading ? (
        width > doubleExtraL ? (
          <View style={styles.content}>
            <View style={styles.contentPadding}>
              {criteriaDropDownIsUsed && <SoritingCriteriaDropDown />}

              {/* Categories */}
              {categories.length > 0 && <CategoriesSelectionDropDown />}

              {/* Price Range */}
              <PriceRangeDropdown />

              {/* Brands */}
              {joinedPageParam.brandSlug &&
              joinedPageParam.brandSlug !== '' ? null : (
                <BrandsSelectionDropDown />
              )}
              <VariationsSelectionDropDown />
            </View>
          </View>
        ) : (
          <ScrollView style={styles.scrollView}>
            <View style={styles.scrollContent}>
              {criteriaDropDownIsUsed && <SoritingCriteriaDropDown />}
              {/* Price Range */}
              <PriceRangeDropdown />
              {/* Categories */}
              <CategoriesSelectionDropDown />
              {/* Brands */}
              {joinedPageParam.brandSlug &&
              joinedPageParam.brandSlug !== '' ? null : (
                <BrandsSelectionDropDown />
              )}
              <VariationsSelectionDropDown />
            </View>
          </ScrollView>
        )
      ) : (
        <SidebarSkeletons />
      )}

      {/* Sticky Footer Section */}
      <View style={styles.footer}>
        <components.Button
          title='Appliquer'
          onPress={() => {
            setFilterIsOpen(false);
          }}
          containerStyle={styles.applyButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 24,
    color: '#666',
  },
  separator: {
    height: 1,
    backgroundColor: '#EFF0F2',
    marginTop: 16,
  },
  content: {
    flex: 1,
  },
  contentPadding: {
    padding: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    maxHeight: Dimensions.get('window').height * 0.6,
  },
  footer: {
    paddingTop: 40,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  applyButton: {
    backgroundColor: theme.colors.mainColor,
    paddingVertical: 24,
  },
});
