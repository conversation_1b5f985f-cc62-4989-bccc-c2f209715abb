import {Text, ScrollView} from 'react-native';
import React from 'react';

import {theme, tabs} from '../../constants';
import {components} from '../../components';
import {useAppSelector} from '../../hooks';

const Wishlist = () => {
  const wishlist = useAppSelector((state) => state.wishlistSlice.list);

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header
        burgerIcon={true}
        basket={true}
        title='Wishlist'
        bottomLine={true}
      />
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
      </components.TabBar>
    );
  };

  const renderProducts = () => {
    return (
      <React.Fragment>
        {wishlist.map((item, index, array) => {
          const lastElement = index === array.length - 1;
          const marginBottom = lastElement ? 0 : 14;

          return (
            <components.WishlistItem
              key={index}
              item={item}
              containerStyle={{marginBottom: marginBottom}}
            />
          );
        })}
      </React.Fragment>
    );
  };

  const renderEmptyWishlist = () => {
    return (
      <React.Fragment>
        <Text>empty</Text>
      </React.Fragment>
    );
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingVertical: 20,
          paddingBottom: 20,
          paddingLeft: 20,
          paddingRight: wishlist.length === 0 ? 20 : 0,
          justifyContent: wishlist.length === 0 ? 'center' : 'flex-start',
        }}
      >
        {wishlist.length === 0 ? renderEmptyWishlist() : renderProducts()}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderTabBar()}
    </components.SmartView>
  );
};

export default Wishlist;
