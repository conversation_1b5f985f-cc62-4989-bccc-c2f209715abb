export interface AddressType {
  id: string;
  firstName: string;
  lastName: string;
  address1: string;
  address2: string;
  zone: string;
  city: {
    id: string;
    name: string;
    country: {
      name: string;
      code: string;
    };
  };
  postalCode: string;
  phone: string;
  company: string;
}

export interface AddressResponseDataType {
  id: string;
  firstName: string;
  lastName: string;
  address1: string;
  address2: string | null;
  zone: string;
  city: {
    id: string;
    name: string;
    country: {
      name: string;
      code: string;
    };
  };
  postalCode: string;
  phone: string;
  company: string | null;
}

export interface CityResponseDataType {
  name: string;
  id: string;
}

export interface City {
  name: string;
  code: string;
}
