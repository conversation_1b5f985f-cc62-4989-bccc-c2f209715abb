import {Text} from 'react-native';
import {PaginationItemType} from '../../types/Pagination';

export function PaginationItem({isActive, ...props}: PaginationItemType) {
  return (
    <div
      className={`border-primary border
      L:p-5 M:p-4
      L:h-9 L:w-9
      M:h-7 M:w-7
      h-[26px] w-[26px]
      sm:h-[13px] sm:w-[13px]
      rounded-sm flex items-center justify-center
      text-gray ${isActive ? 'bg-primary text-white' : 'text-primary'}
      cursor-pointer`}
      {...props}
    >
      <Text>{props.children}</Text>
    </div>
  );
}

export function PaginationEllipsis() {
  return (
    <div
      className={`L:h-9 L:w-9
      M:h-7 M:w-7
      h-[26px] w-[26px]
      sm:h-[24px] sm:w-[24px]
      pb-2 rounded-sm flex items-end justify-center text-gray`}
    >
      <Text>...</Text>
    </div>
  );
}

export function PaginationNext({
  className,
  ...props
}: React.ComponentProps<'button'>) {
  return (
    <button
      className={'text-gray px-3 py-1 sm:px-2 sm:py-1 text-sm sm:text-xs'}
      {...props}
    >
      <Text>{props.children}</Text>
    </button>
  );
}

export function PaginationPrev({
  className,
  ...props
}: React.ComponentProps<'button'>) {
  return (
    <button
      className={'text-gray px-3 py-1 sm:px-2 sm:py-1 text-sm sm:text-xs'}
      {...props}
    >
      <Text>{props.children}</Text>
    </button>
  );
}

export function PaginationList({children}: {children: React.ReactNode[]}) {
  return (
    <div
      className={
        'flex items-center justify-between L:space-x-5 M:space-x-3 space-x-1'
      }
    >
      {children}
    </div>
  );
}
