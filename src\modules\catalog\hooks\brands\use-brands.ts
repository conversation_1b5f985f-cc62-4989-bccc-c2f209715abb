import {keepPreviousData, useQuery} from '@tanstack/react-query';
import {BrandType} from '../../types/brands';
import {retrieveBrandsFromServerSide} from '../../services/brands/brands-extraction';

interface Params {
  categoriesSlugs?: string[];
  productPriceRange?: number[];
  searchByProductName?: string;
  productInStock?: boolean;
  brandsSlugs?: string[];
  productSlugs?: string[];
  search?: string;
  limit: number;
  paginationAffectUrl?: boolean;
}

export default function useBrands({
  limit,
  paginationAffectUrl,
  categoriesSlugs,
  productPriceRange,
  searchByProductName,
  productInStock,
  brandsSlugs,
  productSlugs,
  search,
}: Params) {
  const {data, isLoading, isError} = useQuery<{
    brands: BrandType[];
  } | null>({
    queryKey: [
      'brands',
      limit,
      categoriesSlugs,
      productPriceRange,
      searchByProductName,
      productInStock,
      brandsSlugs,
      productSlugs,
      search,
    ],
    queryFn: () =>
      retrieveBrandsFromServerSide({
        limit,
        categoriesSlugs,
        productPriceRange,
        searchByProductName,
        productInStock,
        brandsSlugs,
        productSlugs,
        search,
      }),
    placeholderData: keepPreviousData,
  });

  return {
    brands: data?.brands || [],
    brandsAreLoading: isLoading,
    brandsError: isError,
  };
}
