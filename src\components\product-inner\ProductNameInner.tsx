import {View, Text} from 'react-native';
import React, {PropsWithChildren} from 'react';

import {text} from '../../text';
import {svg} from '../../assets/svg';
import {theme} from '../../constants';
import {ProductType} from '../../modules/catalog/types/products';

type Props = PropsWithChildren<{item: ProductType}>;

const ProductNameInner: React.FC<Props> = ({item}): JSX.Element => {
  return (
    <View
      style={{
        marginBottom: 30,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
      }}
    >
      <text.H3 style={{marginRight: 'auto'}} numberOfLines={1}>
        {item.name}
      </text.H3>
      <svg.RatingStarSvg />
      <Text
        style={{
          marginLeft: 4,
          ...theme.fonts.DMSans_400Regular,
          color: theme.colors.textColor,
        }}
      >
        5,0
      </Text>
    </View>
  );
};

export default ProductNameInner;
