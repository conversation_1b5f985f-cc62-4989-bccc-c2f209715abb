import {useQuery} from '@tanstack/react-query';
import {useEffect} from 'react';
import {useCheckoutStore} from '../../store/checkout-store';
import {retrieveCities} from '../../services/addresses/cities-extraction';

export default function useCountryCities(countryCode: string) {
  const {city, setCity} = useCheckoutStore((store) => store);
  const {isLoading, data} = useQuery({
    queryKey: [countryCode],
    queryFn: () => retrieveCities(countryCode),
    initialData: [],
    enabled: countryCode !== undefined && countryCode !== '',
  });

  useEffect(() => {
    if (data.length > 0) setCity(data[0]);
  }, [data.length]);

  function changeCity(cityCode: string) {
    const selectedCity = data.find(
      (searchedCity) => searchedCity.code === cityCode,
    );

    if (selectedCity) setCity(selectedCity);
  }

  return {
    cities: data,
    citiesAreLoading: isLoading,
    city,
    changeCity,
  };
}
