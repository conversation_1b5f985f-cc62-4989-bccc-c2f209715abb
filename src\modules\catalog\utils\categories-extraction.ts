import { CategorySelectionType, CategoryType } from "../types/categories";

const getSelectedCategoryWithItsSubCategories = (
  category: CategorySelectionType
) => [
  category,
  ...category.subCategories,
  ...category.subCategories.flatMap((subCat) => subCat.subCategories),
];

const getCategoryWithItsSubCategories = (category: CategoryType) => [
  category,
  ...category.subCategories,
  ...category.subCategories.flatMap((subCat) => subCat.subCategories),
];

export {
  getSelectedCategoryWithItsSubCategories,
  getCategoryWithItsSubCategories,
};
