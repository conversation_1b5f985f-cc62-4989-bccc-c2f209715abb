import { buttonVariants } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { AddressType } from "@/modules/checkout/types/addresses";
import Text from "@/styles/text-styles";

interface Props {
  address: AddressType;
  isSelected: boolean;
}

export function AddressSelectionContainer({ address, isSelected }: Props) {
  return (
    <div
      className={cn(
        buttonVariants({ variant: "ghost" }),
        "py-3 px-3 w-full h-fit border-gray border rounded-2xl flex border-opacity-50 shadow-md text-black",

        { "border-primary bg-primary text-white": isSelected }
      )}
    >
      <div className="w-full flex items-center space-x-4">
        <div
          className={cn("h-6 w-6 border border-gray rounded-full p-1", {
            "border-white": isSelected,
          })}
        >
          {isSelected && (
            <div className="h-[15px] w-[15px]  bg-white rounded-full" />
          )}
        </div>
        <div className="w-full flex flex-col space-y-3">
          <Text textStyle="TS5" className="w-fit font-bold text-xl">
            {`${address.firstName} ${address.lastName}`}
          </Text>
          <div className="flex justify-between">
            <Text textStyle="TS5">
              {`${address.address1} ${address.city?.name} ${
                address.postalCode ? address.postalCode : ""
              }`}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}

export function AddressSelectionContainerSkeleton() {
  return (
    <div
      className={cn(
        "py-3 px-3 w-full h-fit border-gray border rounded-2xl flex border-opacity-50 shadow-md text-black"
      )}
    >
      <div className="w-full flex items-center space-x-4">
        <Skeleton className={cn("h-6 w-6  rounded-full")} />
        <div className="w-full flex flex-col space-y-3">
          <Skeleton className="w-[150px] h-6" />

          <Skeleton className="w-[200px] h-6" />
        </div>
      </div>
    </div>
  );
}
