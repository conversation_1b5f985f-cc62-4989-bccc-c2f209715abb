import Image from "next/image";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import useCartVisibility from "../store/cart-visibility-store";

export default function EmptyCart() {
  const t = useTranslations("shared.cart.emptyState");
  const { setCartIsOpen } = useCartVisibility();

  return (
    <div className="flex-1 flex flex-col items-center justify-center px-6 py-8 space-y-6">
      {/* Empty Cart Image */}
      <div className="w-48 h-48 relative">
        <Image
          src="/empty-cart.png"
          alt="Empty cart"
          fill
          className="object-contain"
          priority
        />
      </div>

      <div className="text-center space-y-3">
        <Text textStyle="TS4" className="font-bold text-black">
          {t("title")}
        </Text>
        <p>
          <Text textStyle="TS7" className="text-gray max-w-sm">
            {t("description")}
          </Text>
        </p>
      </div>

      <Link href="/produits/filtres" onClick={() => setCartIsOpen(false)}>
        <Button className="w-full h-[56px] bg-[#2D3C52] text-white rounded-full text-base  px-12">
          <Text textStyle="TS7">{t("shopNow")}</Text>
        </Button>
      </Link>
    </div>
  );
}
