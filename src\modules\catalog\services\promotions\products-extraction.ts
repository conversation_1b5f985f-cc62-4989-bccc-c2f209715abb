import {CustomError} from '../../../../lib/custom-error';
import {GET} from '../../../../lib/http-methods';
import {ProductInResponseType} from '../../types/products';
import {castToProductType} from '../../utils/types-casting/products';
import {AxiosError} from 'axios';

export async function retrievePromotionProductsFromServerSide(
  limit: number,
  promotionSlug: string,
) {
  try {
    const params = [`limit=${limit}`];

    const res = await GET(
      `promotions/${promotionSlug}/products?${params.join('&')}`,
      {},
    );

    return {
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse),
      ),
    };
  } catch (error) {
    const errorResponse = (error as AxiosError).response as {
      data: {message: string};
      status: number;
    };

    throw new CustomError(
      errorResponse.data?.message as string,
      errorResponse.status as number,
    );
  }
}
