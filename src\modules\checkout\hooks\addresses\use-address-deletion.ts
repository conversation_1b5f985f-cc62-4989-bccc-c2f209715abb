import {useState} from 'react';
import {useQueryClient} from '@tanstack/react-query';
import deleteAddressOnServerSide from '../../services/addresses/address-deletion';

export default function useAddressDeletion() {
  const queryClient = useQueryClient();
  const [deletionIsLoading, setDeletionIsLoading] = useState(false);
  const [deletionPopUpIsOpen, setDeletionPopUpIsOpen] = useState(false);
  const [addressId, setAddressId] = useState('');
  const [warning, setWarning] = useState('');

  function deleteAddress(addressId: string) {
    setAddressId(addressId);
    setDeletionPopUpIsOpen(true);
  }

  function cancelDeletion() {
    setDeletionPopUpIsOpen(false);
    setAddressId('');
  }

  function confirmAddressDeletion() {
    setDeletionIsLoading(true);

    if (warning !== '') setWarning('');

    deleteAddressOnServerSide(addressId).then((res) => {
      if (!res.ok) {
        if (res.status === 401) {
          setWarning('You are not authorized to perform this action.');
        } else {
          setWarning('Something went wrong. Please try again later.');
        }
      } else {
        queryClient.invalidateQueries({queryKey: ['user-address']});
        cancelDeletion();
      }

      setDeletionIsLoading(false);
    });
  }

  return {
    deletionIsLoading,
    deletionPopUpIsOpen,
    confirmAddressDeletion,
    deleteAddress,
    cancelDeletion,
    warning,
  };
}
