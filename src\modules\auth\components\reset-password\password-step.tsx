import WarnInput from "@/components/input/warn-input";
import { useResetPasswordContext } from "@auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function PasswordStep() {
  const t = useTranslations("shared.reset-password");

  const { passwordWarning, submitPassword, isLoading } =
    useResetPasswordContext();

  return (
    <div className={"flex flex-col space-y-1"}>
      <Text textStyle="TS4" className="font-bold text-start text-primary">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS7" className="w-full text-danger">
        {passwordWarning.generalWarning}
      </Text>
      <div className="pt-6 flex flex-col space-y-6">
        <div className="space-y-2">
          <label>
            <Text textStyle="TS6" className="text-gray font-tajawal">
              {t.raw("passwordStep.password")}
            </Text>
          </label>
          <WarnInput
            id="password"
            type="password"
            className="h-[56px] text-base"
            warning={passwordWarning.password}
          />
        </div>
        <div className="space-y-2">
          <label>
            <Text textStyle="TS6" className="text-gray font-tajawal">
              {t.raw("passwordStep.confirmationPassword")}
            </Text>
          </label>
          <WarnInput
            id="confirmationPassword"
            type="password"
            className="h-[56px] text-base"
            warning={passwordWarning.confirmationPassword}
          />
        </div>
        <Button
          className={cn(
            "w-full h-[56px] bg-primary text-white rounded-lg text-base font-semibold",
            { "opacity-70": isLoading }
          )}
          disabled={isLoading}
          onClick={submitPassword}
        >
          <Text textStyle="TS7">{t.raw("passwordStep.button")}</Text>
        </Button>

        {/* Bottom redirect text */}
        <div className="w-full text-center space-y-3 pt-4">
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.haveAccount", {
                loginLink: (chunk) => (
                  <Link
                    href="/auth/sign-in"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.noAccount", {
                signupLink: (chunk) => (
                  <Link
                    href="/auth/sign-up"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
