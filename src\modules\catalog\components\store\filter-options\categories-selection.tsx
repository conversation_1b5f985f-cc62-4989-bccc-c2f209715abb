import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import { CategorySelectionType } from "@/modules/catalog/types/categories";
import FilterChoiceContainer from "./filter-choice-container";
import Text from "@/styles/text-styles";
import { useState } from "react";
import { Plus, Minus } from "lucide-react";

export default function CategoriesSelectionDropDown() {
  const { categories, setCategories, applyFilter } =
    useProductsFilteringStore();
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

  const handleCategoryClick = (choosedCategory: CategorySelectionType) => {
    const selectedCategory = categories.find(
      (category) => category.id === choosedCategory.id
    );

    if (selectedCategory) {
      selectedCategory.selected = !selectedCategory.selected;

      // When category is selected, auto-select all its subcategories
      if (selectedCategory.selected) {
        selectedCategory.subCategories.forEach((sub) => (sub.selected = true));
      } else {
        // If category is deselected, also deselect all its subcategories
        selectedCategory.subCategories.forEach((sub) => (sub.selected = false));
      }
    }

    setCategories([...categories]);
    applyFilter();
  };

  const handleSubCategoryClick = (
    parentCategory: CategorySelectionType,
    subCategory: CategorySelectionType
  ) => {
    const category = categories.find((cat) => cat.id === parentCategory.id);
    if (category) {
      const subCat = category.subCategories.find(
        (sub) => sub.id === subCategory.id
      );
      if (subCat) {
        subCat.selected = !subCat.selected;
        setCategories([...categories]);
        applyFilter();
      }
    }
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const hasSubCategories = (category: CategorySelectionType) => {
    return category.subCategories && category.subCategories.length > 0;
  };

  return (
    <FilterChoiceContainer title="Catégories">
      <div className="space-y-3">
        {categories.map((category) => (
          <div key={category.id}>
            {/* Main Category */}
            <div className="flex items-center justify-between  cursor-pointer pe-3 rounded-md group">
              <div
                className="flex items-center flex-1 space-x-3"
                onClick={() => handleCategoryClick(category)}
              >
                <div
                  className={`w-4 h-4 rounded-md flex items-center justify-center p-0.5 bg-gray-light `}
                >
                  <div
                    className={`w-full h-full rounded-sm flex items-center justify-center  ${
                      category.selected && "bg-blue "
                    }`}
                  ></div>
                </div>

                <Text
                  textStyle="TS6"
                  className={`text-gray-dark font-bold group-hover:font-bold ${
                    category.selected && "text-blue"
                  }`}
                >
                  {category.name}
                </Text>
              </div>

              {hasSubCategories(category) && (
                <button
                  onClick={() => toggleCategoryExpansion(category.id)}
                  className="p-1.5 bg-gray-light rounded-md "
                >
                  {expandedCategories.includes(category.id) ? (
                    <Minus size={16} className="text-gray-dark" />
                  ) : (
                    <Plus size={16} className="text-gray-dark" />
                  )}
                </button>
              )}
            </div>

            {/* Subcategories */}
            {hasSubCategories(category) &&
              expandedCategories.includes(category.id) && (
                <div className="ml-4 space-y-1 border-l-2 border-gray-light pl-3 mt-3">
                  {category.subCategories.map((subCategory) => (
                    <div
                      key={subCategory.id}
                      className="flex items-center py-1 cursor-pointer hover:bg-gray-50 rounded-md px-2 space-x-3"
                      onClick={() =>
                        handleSubCategoryClick(category, subCategory)
                      }
                    >
                      <div
                        className={`w-4 h-4 rounded-md flex items-center justify-center p-0.5 bg-gray-light `}
                      >
                        <div
                          className={`w-full h-full rounded-sm flex items-center justify-center  ${
                            subCategory.selected && "bg-blue "
                          }`}
                        ></div>
                      </div>

                      <Text
                        textStyle="TS7"
                        className={`text-gray font-bold ${
                          subCategory.selected ? "text-blue" : "text-gray-dark"
                        }`}
                      >
                        {subCategory.name}
                      </Text>
                    </div>
                  ))}
                </div>
              )}
          </div>
        ))}
      </div>
    </FilterChoiceContainer>
  );
}
