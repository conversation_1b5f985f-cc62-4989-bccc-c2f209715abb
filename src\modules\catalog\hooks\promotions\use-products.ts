import {keepPreviousData, useQuery} from '@tanstack/react-query';
import {ProductType} from '../../types/products';
import {retrievePromotionProductsFromServerSide} from '../../services/promotions/products-extraction';
import {CustomError} from '../../../lib/custom-error';

export default function usePromotionProducts({
  limit,
  promotionSlug,
}: {
  limit: number;
  promotionSlug: string;
}) {
  const {data, isLoading, error} = useQuery<
    {
      products: ProductType[];
    } | null,
    CustomError
  >({
    queryKey: ['promotion-products', promotionSlug],
    queryFn: () =>
      retrievePromotionProductsFromServerSide(limit, promotionSlug),
    placeholderData: keepPreviousData,
  });

  return {
    products: data?.products,
    productsAreLoading: isLoading,
    error,
  };
}
