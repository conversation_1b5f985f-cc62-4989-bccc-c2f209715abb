import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON>} from '@/components/ui/scroll-area';
import {cn} from '@/lib/utils';
import Image from 'next/image';
import {HTMLAttributes, useEffect, useState} from 'react';
import {ItemType} from '../../../../types/products';
import {Skeleton} from '@/components/ui/skeleton';
import {getPromotionPercentage} from '../../../../utils/promotion-percentage';
import Text from '@/styles/text-styles';
import {Button} from '@/components/ui/button';
import {ChevronLeft, ChevronRight} from 'lucide-react';

interface Props extends HTMLAttributes<'div'> {
  productItem: ItemType | null;
}

export default function ProductCarousel({productItem, ...props}: Props) {
  const [productImage, setProductImage] = useState(
    'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image',
  );
  const [selectedImage, setSelectedImage] = useState(-1);
  const promotionPercentage = productItem
    ? getPromotionPercentage(
        productItem?.prices[0].realPrice,
        productItem?.prices[0].promotionalPrice,
      )
    : '0';

  useEffect(() => {
    if (productItem)
      setProductImage(
        (selectedImage > -1
          ? productItem.images[selectedImage]
          : productItem.image) ||
          'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image',
      );
  }, [productItem, selectedImage]);

  const handlePrevImage = () => {
    if (!productItem) return;

    if (selectedImage === -1) {
      // If main image is selected, go to the last image in the array
      setSelectedImage(productItem.images.length - 1);
    } else if (selectedImage > 0) {
      // Go to previous image
      setSelectedImage(selectedImage - 1);
    } else {
      // If at first image, go to main image
      setSelectedImage(-1);
    }
  };

  const handleNextImage = () => {
    if (!productItem) return;

    if (selectedImage === -1) {
      // If main image is selected, go to the first image in the array
      if (productItem.images.length > 0) {
        setSelectedImage(0);
      }
    } else if (selectedImage < productItem.images.length - 1) {
      // Go to next image
      setSelectedImage(selectedImage + 1);
    } else {
      // If at last image, go back to main image
      setSelectedImage(-1);
    }
  };

  return productItem ? (
    <div
      className={cn(
        'w-full flex flex-col items-center space-y-6',
        props.className,
      )}
    >
      <div className='max-w-[500px] w-full aspect-square rounded-lg flex items-center justify-center relative bg-white group'>
        <Image
          onError={() =>
            setProductImage(
              'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image',
            )
          }
          src={productImage}
          alt={'product-image'}
          fill
          unoptimized
          className='rounded-lg object-cover'
        />
        {promotionPercentage != '0' && (
          <div className='bg-danger text-white px-2 py-1 text-xs font-medium rounded absolute top-3 right-3'>
            <Text textStyle='TS8'>{`-${promotionPercentage}%`}</Text>
          </div>
        )}

        {/* Navigation Buttons - Only show if there are multiple images */}
        {productItem && productItem.images.length > 0 && (
          <>
            {/* Previous Button */}
            <Button
              onClick={handlePrevImage}
              variant='outline'
              size='icon'
              className='absolute left-3 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white/90 border border-primary text-primary hover:bg-primary hover:text-white shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200'
            >
              <ChevronLeft className='h-5 w-5' />
              <span className='sr-only'>Previous image</span>
            </Button>

            {/* Next Button */}
            <Button
              onClick={handleNextImage}
              variant='outline'
              size='icon'
              className='absolute right-3 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white/90 border border-primary text-primary hover:bg-primary hover:text-white shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200'
            >
              <ChevronRight className='h-5 w-5' />
              <span className='sr-only'>Next image</span>
            </Button>
          </>
        )}
      </div>
      <ScrollArea className='w-[280px] S:w-full'>
        <div className='flex regularL:space-x-4 space-x-3'>
          {productItem.images && productItem.images.length > 0 && (
            <button
              onClick={() => setSelectedImage(-1)}
              className={cn(
                'relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden hover:border-gray-400',
                selectedImage === -1
                  ? 'border-2 border-primary'
                  : 'border border-transparent',
              )}
            >
              <Image
                src={
                  productItem.image ||
                  'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image'
                }
                alt={'product-image'}
                unoptimized
                fill
                className='object-cover'
              />
            </button>
          )}
          {productItem.images.map((image, index) => (
            <button
              key={index}
              onClick={() => setSelectedImage(index)}
              className={cn(
                'relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden hover:border-gray-400',
                selectedImage === index
                  ? 'border-2 border-primary'
                  : 'border border-transparent',
              )}
            >
              <Image
                src={
                  image ||
                  'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image'
                }
                alt={'product-image'}
                unoptimized
                fill
                className='object-cover'
              />
            </button>
          ))}
        </div>
        <ScrollBar orientation='horizontal' />
      </ScrollArea>
    </div>
  ) : (
    <div
      className={cn(
        'w-full regularL:max-w-full max-w-[500px] flex flex-col space-y-6',
        props.className,
      )}
    >
      <Skeleton className='w-full aspect-square rounded-xl' />
      <div className='flex regularL:space-x-4 space-x-3'>
        {Array.from({length: 4}).map((_, idx) => (
          <Skeleton
            key={idx}
            className='relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden'
          />
        ))}
      </div>
    </div>
  );
}
