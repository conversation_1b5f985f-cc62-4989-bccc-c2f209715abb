import {POST} from '@/lib/http-methods';
import {AxiosError} from 'axios';
import {CustomError} from '../../../utils/custom-error';
import {CouponCodeType} from '../types';
import extractJWTokens from '@/modules/auth/utils/jwt/extract-tokens';

export async function pointsToCouponOnServerSide(
  points: number,
): Promise<CouponCodeType | null> {
  try {
    const {access} = extractJWTokens();
    const headers = {
      Authorization: `Bearer ${access}`,
    };

    const res = await POST('/voucher-codes/from-points/register', headers, {
      points,
    });

    return res.data;
  } catch (error) {
    const axiosError = error as AxiosError<{code: string; message: string}>;

    if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === 'P5062') {
        throw new CustomError(
          'pointsExceedsPointsInAccount',
          axiosError.response?.status,
          axiosError.response?.data.code,
        );
      } else if (axiosError.response?.data.code === 'P1000') {
        throw new CustomError(
          'invalidData',
          axiosError.response?.status,
          axiosError.response?.data.code,
        );
      }

      throw new CustomError('Server Error!', 500);
    }

    return null;
  }
}
