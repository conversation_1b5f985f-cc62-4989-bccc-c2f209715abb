"use client";
import Text from "@/styles/text-styles";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import usePasswordChangement from "../../hooks/use-password-changement";
import { cn } from "@/lib/utils";
import WarnInput from "@/components/input/warn-input";
import { Button } from "@/components/ui/button";
import useUserStore from "../../store/user-store";

export default function AccountSettings() {
  const { isLoading: userIsLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage.accountSettings");
  const { submitPassword, warning, formRef, passwordChanged } =
    usePasswordChangement();

  return !userIsLoading ? (
    <div className={cn("flex flex-col space-y-16")}>
      <div className={cn("flex flex-col space-y-7")}>
        <Text textStyle="TS2" className="text-primary font-bold text-xl">
          {t("title")}
        </Text>
        <form ref={formRef} className="flex flex-col space-y-7">
          {warning.getGeneralWarning !== "" ? (
            <Text textStyle="TS6" className="text-danger">
              {warning.getGeneralWarning}
            </Text>
          ) : null}

          <label className="M:w-fit w-full flex flex-col space-y-2">
            <Text textStyle="TS5" className="text-primary font-tajawal">
              {t("previousPassword")}
            </Text>
            <WarnInput
              name="currentPassword"
              className={cn(
                "text-primary font-tajawal extraL:min-w-[450px] M:min-w-[350px] border border-primary rounded-lg "
              )}
              type="password"
              placeholder="*********"
              warning={warning.currentPassword}
            />
          </label>

          <label className="M:w-fit w-full flex flex-col space-y-2">
            <Text textStyle="TS5" className="text-primary font-tajawal">
              {t("newPassword")}
            </Text>
            <WarnInput
              name="newPassword"
              className={cn(
                "text-primary font-tajawal extraL:min-w-[450px] M:min-w-[350px] border border-primary rounded-lg "
              )}
              type="password"
              placeholder="*********"
              warning={warning.newPassword}
            />
          </label>

          <label className="M:w-fit w-full flex flex-col space-y-2">
            <Text textStyle="TS5" className="text-primary font-tajawal">
              {t("confirmationPassword")}
            </Text>
            <WarnInput
              name="confirmationPassword"
              className={cn(
                "text-primary font-tajawal extraL:min-w-[450px] M:min-w-[350px] border border-primary rounded-lg "
              )}
              type="password"
              placeholder="*********"
              warning={warning.confirmationPassword}
            />
          </label>

          <Button
            onClick={submitPassword}
            className={cn(
              "M:w-fit w-full h-11 extraL:min-w-[450px] M:min-w-[350px] rounded-xl flex justify-center items-center active:scale-95 group bg-primary",
              {
                "scale-95": userIsLoading,
              }
            )}
            disabled={userIsLoading || passwordChanged}
          >
            {passwordChanged ? (
              <div className="flex items-center space-x-3">
                <Text
                  textStyle="TS5"
                  className="text-white font-tajawal group-hover:text-primary"
                >
                  {t("buttons.changementConfirmed")}
                </Text>
              </div>
            ) : (
              <Text
                textStyle="TS5"
                className="text-white font-tajawal group-hover:text-primary"
              >
                {t("buttons.button")}
              </Text>
            )}
          </Button>
        </form>
      </div>
    </div>
  ) : (
    <div className="flex flex-col space-y-16">
      <Skeleton className="w-52 h-10" />

      <div className={cn("flex flex-col space-y-14")}>
        <Skeleton className="M:w-72 w-52 h-10" />

        <div className="flex flex-col space-y-7">
          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="M:w-80 w-full h-10" />
          </div>

          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="M:w-80 w-full h-10" />
          </div>

          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="M:w-80 w-full h-10" />
          </div>

          <Skeleton className="M:w-80 w-full h-10" />
        </div>
      </div>
    </div>
  );
}
