import Text from "@/styles/text-styles";
import { ReactElement, useEffect, useState } from "react";
import {
  getSelectedCategories,
  useProductsFilteringStore,
} from "@/modules/catalog/store/products-filter";
import { useTranslations } from "next-intl";
import FilterIcon from "@assets/icons/filter";
import { Button } from "@/components/ui/button";
import useBrands from "../../../hooks/brands/use-brands";
import FilterDrawer from "../filter-drawer";
import { usePathname, useSearchParams } from "next/navigation";
import useMinMaxPrice from "../../../hooks/products/use-min-max-price";
import useCategory from "@/modules/catalog/hooks/categories/use-category";
import BreadCrumb from "@/components/breadcrumb";
import SortingOptions from "../sorting-options";

interface Props {
  children: ReactElement;
  categorySlug: string;
  productCount?: number;
}

export default function CategoryPageProductsWrapper({
  children,
  categorySlug,
  productCount,
}: Props) {
  const t = useTranslations("filtersPage");
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const brandsIdsInParams = searchParams.get("brandsSlugs");
  const defaultSelectedBrandsFromParams = brandsIdsInParams
    ? brandsIdsInParams.split(",")
    : [];
  const categoriesSlugsFromParams = searchParams.get("categoriesSlugs");
  const defaultSelectedCategoriesFromParams = categoriesSlugsFromParams
    ? categoriesSlugsFromParams.split(",")
    : [];

  const {
    category: extractedCategory,
    categoryIsLoading: extractedCategoryAreLoading,
  } = useCategory({ categorySlug });

  const { prices: minMaxPrices } = useMinMaxPrice();

  const {
    setJoinedPageData,
    search,
    setPriceRange,
    setBrands,
    initializeCategories,
    priceRange,
  } = useProductsFilteringStore();

  const { brands, brandsAreLoading } = useBrands({
    limit: 500,
    categoriesSlugs: getSelectedCategories().map((cat) => cat.slug),
    productPriceRange: priceRange,
  });

  const [pageTitle, setPageTitle] = useState(t("title"));
  const [filterIsOpen, setFilterIsOpen] = useState(false);

  /*
  this part will be responsible to display the applied buttons
  and filter the products based on the passed parameters
  */

  /* initial price slider set up */
  useEffect(() => {
    const priceRangeInParam = searchParams.get("priceRange");

    const initialPricesSetup = () => {
      if (priceRangeInParam) {
        const prices = priceRangeInParam
          .split(",")
          .map((price) => Number(price));

        if (prices.length === 2) setPriceRange([prices[0], prices[1]]); // set the price range state

        return;
      }

      setPriceRange([minMaxPrices[0], minMaxPrices[1]]);
    };

    initialPricesSetup();
  }, [...minMaxPrices]);

  //the page joined is a brand page
  useEffect(() => {
    if (!brandsAreLoading && brands) {
      if (defaultSelectedBrandsFromParams.length > 0) {
        //we're not in a brand page so we need to update the filter based on the passed params
        const brandsFromParams = brands.filter((brand) =>
          defaultSelectedBrandsFromParams.includes(brand.slug)
        );

        if (brandsFromParams.length > 0) {
          setBrands(brandsFromParams);
        }
      }
    }
  }, [brands, brandsAreLoading, pathname]);

  useEffect(() => {
    // the page joined is category page
    if (!extractedCategoryAreLoading && extractedCategory) {
      //joined page is a category page
      setPageTitle(extractedCategory.name);
      setJoinedPageData({ category: extractedCategory });

      initializeCategories(
        extractedCategory.subCategories,
        defaultSelectedCategoriesFromParams
      );
    }
  }, [extractedCategory, categorySlug, extractedCategoryAreLoading, pathname]);

  return (
    <div className="w-full flex flex-col gap-3 ">
      <BreadCrumb />
      <div className="flex flex-col regularL:flex-row items-center">
        {search !== "" ? (
          <h1 className="w-full flex justify-start text-primary ">
            <Text textStyle="TS2" className="font-bold">
              {search && (
                <span>
                  {t.raw("search")} : &quot; {search} &quot;
                  {productCount && (
                    <span className="text-gray ml-2">({productCount})</span>
                  )}
                </span>
              )}
            </Text>
          </h1>
        ) : (
          <h1 className="w-full flex justify-start text-gray-dark font-bold">
            <Text textStyle="TS2">{pageTitle}</Text>
            {productCount && (
              <span className="text-gray ml-2">({productCount})</span>
            )}
          </h1>
        )}
        <SortingOptions />
      </div>

      {/* Sidebar */}
      <div className="pt-3 w-full flex justify-between">
        <Button
          variant="link"
          className="2extraL:hidden L:px-0 L:hover:before:w-full hover:before:w-0 L:hover:bg-transparent L:hover:text-primary before:bg-primary hover:bg-primary hover:text-white L:bg-transparent bg-primary L:text-primary text-white L:relative L:bottom-0 L:right-0 L:z-0 fixed bottom-20 left-5 w-fit L:shadow-none shadow-md z-50"
          onClick={() => setFilterIsOpen(true)}
        >
          <span className="L:stroke-primary stroke-white">
            <FilterIcon />
          </span>
          <Text textStyle="TS5" className="font-bold">
            {t("filterHeader")}
          </Text>
        </Button>
      </div>

      <div className="h-full 2extraL:flex 2extraL:space-x-5">
        <div className="h-fit XL:max-w-[300px] 2extraL:max-w-[250px] w-full rounded-xl border-primary">
          <FilterDrawer
            setFilterIsOpen={setFilterIsOpen}
            filterIsOpen={filterIsOpen}
            isLoading={brandsAreLoading && extractedCategoryAreLoading}
          />
        </div>

        <div className="w-full min-h-full">{children}</div>
      </div>
    </div>
  );
}
