import { Button } from "@/components/ui/button";
import useMinMaxPrice from "@/modules/catalog/hooks/products/use-min-max-price";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import Text from "@/styles/text-styles";
import FilterChoiceContainer from "./filter-choice-container";
import { Slider } from "@/components/ui/slider";
import { useTranslations } from "next-intl";

export default function PriceRangeDropdown() {
  const { priceRange, setPriceRange, applyPriceRangeFiltering } =
    useProductsFilteringStore();
  const { prices: minMaxPrices } = useMinMaxPrice();
  const { currency } = useCurrency();

  const handleSliderChange = (value: [number, number]) => {
    setPriceRange(value);
  };

  const t = useTranslations("filtersPage");

  return (
    <FilterChoiceContainer title="Par Prix">
      <div className="flex flex-col space-y-5">
        {/* Slider Section */}
        <div className="space-y-4">
          <Slider
            value={priceRange}
            min={minMaxPrices[0]}
            max={minMaxPrices[1]}
            step={1}
            onValueChange={handleSliderChange}
            className="w-full   mt-5"
          />
          {/* Price Range Display */}
          <div className="flex justify-center items-center gap-4">
            <div className="bg-gray-light px-4 py-2 rounded-full">
              <Text textStyle="TS6" className="text-gray-dark font-medium">
                {priceRange.length > 0 ? priceRange[0] : minMaxPrices[0]}{" "}
                {currency}
              </Text>
            </div>
            <Text textStyle="TS6" className="text-gray-dark font-bold">
              -
            </Text>
            <div className="bg-gray-light px-4 py-2 rounded-full">
              <Text textStyle="TS6" className="text-gray-dark font-medium">
                {priceRange.length > 1 ? priceRange[1] : minMaxPrices[1]}{" "}
                {currency}
              </Text>
            </div>
          </div>
        </div>

        {/* Filter Button */}
        <Button
          variant="ghost"
          onClick={applyPriceRangeFiltering}
          className="w-fit rounded-md py-3 font-semibold transition-colors border-0 shadow-none underline decoration-blue px-0"
        >
          <Text textStyle="TS5" className="text-blue font-semibold">
            {t("filterHeader")}
          </Text>
        </Button>
      </div>
    </FilterChoiceContainer>
  );
}
