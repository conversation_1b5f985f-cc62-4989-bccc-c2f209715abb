import {useEffect} from 'react';
import useUserStore from '../store/user-store';
import {useAppNavigation} from '../../../hooks';
import {getItemAsync, setItemAsync} from 'expo-secure-store';

interface Params {
  delay: number; // delay in ms
}

export default function useDelaySignUpPopup({delay}: Params) {
  const navigation = useAppNavigation();
  const {user} = useUserStore((store) => store);

  useEffect(() => {
    const handlePopup = async () => {
      const alreadyDisplayed = await getItemAsync('signUpPopup');

      if (alreadyDisplayed !== 'displayed') {
        setTimeout(async () => {
          if (!user || (user && !user.isAuthenticated)) {
            navigation.navigate('SignUp');
            await setItemAsync('signUpPopup', 'displayed');
          }
        }, delay);
      }
    };

    handlePopup();
  }, [delay, navigation, user]);
}
