"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import XSymbolIcon from "@assets/icons/x-symbol";
import useUserOrders from "@/modules/checkout/hooks/use-orders";
import PaginationMangement from "@/components/pagination/pagination-management";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import useUserStore from "@/modules/auth/store/user-store";
import OrderHistoryContainer from "./container/history";

export default function OrdersHistory() {
  const { orders, ordersAreLoading, currentPage, pagesNumber, setPage } =
    useUserOrders(9);
  const { isLoading: userIsLoading } = useUserStore((store) => store);

  const t = useTranslations("accountPage.accountOrders");

  return (!userIsLoading || !ordersAreLoading) && orders ? (
    <div className="flex flex-col space-y-16 text-primary">
      <div
        className={cn("flex flex-col space-y-14", {
          "items-center": orders.length === 0,
        })}
      >
        {orders.length !== 0 && true ? (
          <div className="flex flex-col extraL:space-y-0 space-y-[30px]">
            <Text
              textStyle="TS3"
              className="text-primary font-tajawal extraL:hidden"
            >
              {t("title")}
            </Text>
            <div className="max-w-[800px] w-full flex flex-col XL:space-y-[88px] space-y-[35px]">
              <div className="w-full flex flex-col space-y-10">
                {orders.map((order, idx) => (
                  <OrderHistoryContainer key={idx} details={order} />
                ))}
              </div>

              <div className="w-full flex justify-center">
                <PaginationMangement
                  currentPage={currentPage}
                  pagesNumber={pagesNumber}
                  changePage={setPage}
                />
              </div>
            </div>
          </div>
        ) : (
          <EmptyOrders />
        )}
      </div>
    </div>
  ) : (
    <div className="flex flex-col space-y-10">
      <Skeleton className="w-52 h-10" />

      {Array.from({ length: 3 }).map((_, idx) => (
        <div key={idx} className="w-full flex flex-col space-y-2">
          <div className="w-full flex justify-between">
            <Skeleton className="w-40 h-3" />
          </div>
          <Skeleton className="w-40 h-3" />
          <Skeleton className="w-40 h-3" />
          <Skeleton className="w-52 h-3" />
          {Array.from({ length: 3 }).map((_, idx) => (
            <Skeleton key={idx} className="w-60 h-3" />
          ))}
          <Skeleton className="w-52 h-3" />
          <Skeleton className="w-40 h-3" />
          <Skeleton className="w-52 h-3" />
          <Skeleton className="w-52 h-3" />
          <Skeleton className="M:w-72 w-full h-3" />
          <Skeleton className="w-40 h-3" />
          <Skeleton className="w-32 h-3" />
        </div>
      ))}
    </div>
  );
}

function EmptyOrders() {
  const { user } = useUserStore((store) => store);
  const t = useTranslations("accountPage.accountOrders.emptyOrders");
  const sharedButtons = useTranslations("landingPage.productsOverview");

  return (
    <div className="w-full px-4 flex flex-col space-y-10 justify-center items-center ">
      <div className="w-[116px] h-[116px] bg-primary bg-opacity-20 rounded-full flex justify-center items-center">
        <div className="w-[96px] h-[96px] bg-primary bg-opacity-50 rounded-full flex justify-center items-center">
          <div className="w-[76px] h-[76px] bg-primary rounded-full flex justify-center items-center">
            <XSymbolIcon />
          </div>
        </div>
      </div>
      <div className={cn("L:w-[80%] flex flex-col items-center space-y-9", {})}>
        <Text textStyle="TS3" className="font-bold font-tajawal text-center">
          {t.rich("title", {
            name: (chunk) => <span>{user?.name}</span>,
          })}
        </Text>
        <Text textStyle="TS4" className="font-tajawal text-center">
          {t("description")}
        </Text>
        <Link href={"/produits/filtres"}>
          <Button
            variant="voirplus"
            size="sm"
            className="bg-primary text-white hover:text-primary hover:border-primary"
          >
            <Text textStyle="TS5">{sharedButtons("discoverPlus")}</Text>
          </Button>
        </Link>
      </div>
    </div>
  );
}
