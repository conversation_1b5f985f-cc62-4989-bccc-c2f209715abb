import {getPasswordsWarnings} from '../../utils/warnings/input-warning';
import {getPasswordConfirmationSchema} from '../schemas/auth/password-confirmation';

type ResponseType = {
  ok: boolean;
  password: string;
  warning: {
    password: string;
    confirmationPassword: string;
    generalWarning: string;
  };
};

export function verifyPasswords(): ResponseType {
  const password = document.getElementById('password') as HTMLInputElement;
  const confirmationPassword = document.getElementById(
    'confirmationPassword',
  ) as HTMLInputElement;

  if (password && confirmationPassword) {
    const passwordConfirmationSchema = getPasswordConfirmationSchema();
    const verificationResult = passwordConfirmationSchema.safeParse({
      password: password.value,
      confirmationPassword: confirmationPassword.value,
    });

    if (!verificationResult.success) {
      return {
        ok: false,
        password: '',
        warning: getPasswordsWarnings(verificationResult),
      };
    } else
      return {
        ok: true,
        password: password.value,
        warning: {
          password: '',
          confirmationPassword: '',
          generalWarning: '',
        },
      };
  }

  return {
    ok: false,
    password: '',
    warning: {
      password: '',
      confirmationPassword: '',
      generalWarning: '',
    },
  };
}
