import { useQuery } from "@tanstack/react-query";
import {
  retrieveGuestUserOrderDetails,
  retrieveUserOrderDetails,
} from "../services/orders/order-details";
import { OrderDataType } from "../types/orders";
import { CustomError } from "../../../utils/custom-error";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import useUserStore from "@/modules/auth/store/user-store";

export default function useOrderDetail(orderId: string) {
  const { user, isLoading: userIsLoading } = useUserStore((store) => store);
  const { setIsOpen: setAuthPopUpIsOpen } = useAuthDialogState();
  const { data, isLoading, error } = useQuery<
    OrderDataType | null,
    CustomError
  >({
    queryKey: [orderId, user, userIsLoading],
    queryFn: () =>
      user !== null && user.isAuthenticated
        ? retrieveUserOrderDetails(orderId)
        : retrieveGuestUserOrderDetails(orderId),
  });

  if (error && error.status === 401) {
    setAuthPopUpIsOpen(true);
  }

  return {
    order: data,
    isLoading,
  };
}
