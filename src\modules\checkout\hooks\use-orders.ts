import {useQuery} from '@tanstack/react-query';
import {useEffect, useState} from 'react';
import {retrieveUserOrders} from '../services/orders/orders-extraction';
import {getCurrentRoute, useAppNavigation} from '../../../hooks';

export default function useUserOrders(limit: number) {
  const navigation = useAppNavigation();
  const pathname = getCurrentRoute(navigation.getState());
  const [page, setPage] = useState(1);
  const [pagesNumber, setPagesNumber] = useState(1);
  const {data, isLoading, isError} = useQuery({
    queryKey: ['user-orders', page],
    queryFn: () => retrieveUserOrders(page, limit),
    enabled: !['my-account', 'my-account/info', 'my-account/settings'].every(
      (disabledPathname) => pathname.name.endsWith(disabledPathname),
    ),
  });

  useEffect(() => {
    if (data)
      setPagesNumber(
        data?.pagination?.totalPages ? data?.pagination?.totalPages : 1,
      );
  }, [data]);

  return {
    orders: data ? data.orders : null,
    ordersAreLoading: isLoading,
    pagesNumber,
    currentPage: page,
    setPage,
  };
}
