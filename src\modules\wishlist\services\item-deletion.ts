import {AxiosError} from 'axios';
import extractJWTokens from '../../auth/utils/jwt/extract-tokens';
import {refreshToken} from '../../auth/services/refresh-token';
import {CustomError} from '../../../lib/custom-error';
import {DELETE} from '../../../lib/http-methods';

export async function deleteWishedItemOnServerSide(itemId: string) {
  const {access} = await extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    await DELETE(`/favourites/products/${itemId}`, headers);
    return {ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() =>
        deleteWishedItemOnServerSide(itemId),
      );
      if (!res) throw new CustomError('Unauthorized', 401);
      return res;
    } else if (axiosError.response?.status == 404) {
      throw new CustomError('Product Not Found!', 404);
    } else {
      throw new CustomError('Server Error!', 500);
    }
  }
}

export async function clearWishlistOnServerSide(wishlistItemIds: string[]) {
  const {access} = await extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const deletePromises = wishlistItemIds.map(async (itemId) => {
      try {
        await DELETE(`/favourites/products/${itemId}`, headers);
        return {success: true, itemId};
      } catch (error) {
        const axiosError = error as AxiosError;
        if (axiosError.response?.status == 401) {
          const res = await refreshToken(() =>
            DELETE(`/favourites/products/${itemId}`, headers),
          );
          if (!res) throw new CustomError('Unauthorized', 401);
          return {success: true, itemId};
        } else if (axiosError.response?.status == 404) {
          return {success: true, itemId};
        } else {
          throw new CustomError('Server Error!', 500);
        }
      }
    });

    await Promise.all(deletePromises);
    return {ok: true};
  } catch (error) {
    throw error;
  }
}
