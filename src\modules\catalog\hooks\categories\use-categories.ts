import { useQuery } from "@tanstack/react-query";
import { CategoryType } from "../../types/categories";
import { retrieveCategoriesFromServerSide } from "../../services/categories/categories-extraction";

export default function useCategories() {
  const { data, isLoading, isError } = useQuery<CategoryType[] | null>({
    queryKey: ["categories"],
    queryFn: () => retrieveCategoriesFromServerSide(),
  });

  return {
    categories: data,
    categoriesAreLoading: isLoading,
    categoriesError: isError,
  };
}
