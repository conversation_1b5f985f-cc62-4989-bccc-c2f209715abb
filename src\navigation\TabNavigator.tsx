import React from 'react';

import {screens} from '../screens';
import {useAppSelector} from '../hooks';

const TabNavigator: React.FC = (): JSX.Element => {
  const currentTabScreen = useAppSelector((state) => state.tab.screen);

  const renderScreen = () => {
    return (
      <React.Fragment>
        {currentTabScreen === 'Home' && <screens.Home />}
        {currentTabScreen === 'Search' && <screens.Categories />}
        {currentTabScreen === 'Order' && <screens.Order />}
        {currentTabScreen === 'Wishlist' && <screens.Wishlist />}
        {currentTabScreen === 'Profile' && <screens.Profile />}
      </React.Fragment>
    );
  };

  return renderScreen();
};

export default TabNavigator;
