import {ViewStyle} from 'react-native';
import React from 'react';
import {homeIndicatorHeight} from '../../utils';
import {theme} from '../../constants';
import {LinearGradient} from 'expo-linear-gradient';

type Props = {children: React.ReactNode};

const TabBar: React.FC<Props> = ({children}): JSX.Element => {
  const homeIndicatorSettings = () => {
    if (homeIndicatorHeight() !== 0) {
      return 10 + homeIndicatorHeight();
    }
    if (homeIndicatorHeight() === 0) {
      return 20 + homeIndicatorHeight();
    }
  };

  const tabBarStyle: ViewStyle = {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    alignItems: 'center',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    backgroundColor: theme.colors.white,
    paddingBottom: homeIndicatorSettings(),
  };

  // return (
  //   <View style={{...tabBarStyle}}>
  //     {tabs.map((item: TabBarType, index: number, array: TabBarType[]) => {
  //       return <components.TabBarItem key={index} item={item} />;
  //     })}
  //   </View>
  // );

  return (
    <LinearGradient
      colors={['#142535', '#010202']}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 24,
        borderTopColor: '#EEEEEE',
        backgroundColor: theme.colors.mainColor,
        paddingBottom: homeIndicatorSettings(),
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
      }}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
    >
      {children}
    </LinearGradient>
  );
};

export default TabBar;
