"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { User } from "lucide-react";
import { useTranslations } from "next-intl";
import useUserStore from "../../store/user-store";

export default function UserWelcome() {
  const { user, isLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage.welcoming");

  return !isLoading ? (
    <div className="w-full flex flex-col space-y-10 justify-center items-center text-primary">
      <div className="w-[116px] h-[116px] bg-primary bg-opacity-20 rounded-full flex justify-center items-center">
        <div className="w-[96px] h-[96px] bg-primary bg-opacity-50 rounded-full flex justify-center items-center">
          <div className="w-[76px] h-[76px] bg-primary rounded-full flex justify-center items-center text-white">
            <User width="25" height="26" />
          </div>
        </div>
      </div>
      <div className={cn("L:w-[80%] flex flex-col items-center space-y-9", {})}>
        <Text textStyle="TS3" className="font-bold text-center ">
          {t.rich("title", {
            name: (chunk) => <span>{user?.name}</span>,
          })}
        </Text>
        <Text textStyle="TS4" className="text-center ">
          {t("description")}
        </Text>
      </div>
    </div>
  ) : (
    <div className="px-2 w-full flex flex-col space-y-10 justify-center items-center text-primary">
      <Skeleton className="w-[116px] h-[116px] rounded-full" />
      <div
        className={cn(
          "L:w-[80%] w-full flex flex-col items-center space-y-9",
          {}
        )}
      >
        <Skeleton className="w-32 h-10" />
        <div className="M:w-72 w-full flex flex-col space-y-1">
          {Array.from({ length: 4 }).map((_, idx) => (
            <Skeleton key={idx} className="w-full h-10" />
          ))}
        </div>
      </div>
    </div>
  );
}
