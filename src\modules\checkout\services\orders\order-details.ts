import {castToOrderType} from '../../utils/types-casting/order';
import {OrderDataType, OrderResponseDataType} from '../../types/orders';
import {AxiosError} from 'axios';
import {GET} from '../../../../lib/http-methods';
import extractJWTokens from '../../../auth/utils/jwt/extract-tokens';
import {refreshToken} from '../../../auth/services/refresh-token';
import {CustomError} from '../../../../lib/custom-error';

export async function retrieveGuestUserOrderDetails(
  orderId: string,
): Promise<OrderDataType | null> {
  try {
    const res = await GET(`/orders/guest/${orderId}`, {});
    return castToOrderType(res.data as OrderResponseDataType);
  } catch (error) {
    return null;
  }
}

export async function retrieveUserOrderDetails(
  orderId: string,
): Promise<OrderDataType | null> {
  const {access} = await extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`/orders/authenticated/${orderId}`, headers);
    return castToOrderType(res.data as OrderResponseDataType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveUserOrderDetails(orderId));

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError('Unauthorized!', 401);

      return res;
    }

    return null;
  }
}
