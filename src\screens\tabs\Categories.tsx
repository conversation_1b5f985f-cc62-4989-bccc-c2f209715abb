import React from 'react';
import {responsiveWidth} from 'react-native-responsive-dimensions';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';

import {text} from '../../text';
import {components} from '../../components';
import {theme, tabs} from '../../constants';
import {
  useGetTagsQuery,
  useGetProductsQuery,
  useGetCategoriesQuery,
} from '../../store/slices/apiSlice';
import {setTag} from '../../store/slices/tagSlice';
import {useAppSelector, useAppDispatch, useAppNavigation} from '../../hooks';

const Categories: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();
  const {
    data: productsData,
    error: productsError,
    isLoading: productsLoading,
  } = useGetProductsQuery();

  const {
    data: categoriesData,
    error: categoriesError,
    isLoading: categoriesLoading,
  } = useGetCategoriesQuery();

  const {
    data: tagsData,
    error: tagsError,
    isLoading: tagsLoading,
  } = useGetTagsQuery();

  const tags = tagsData instanceof Array ? tagsData : [];
  const products = productsData instanceof Array ? productsData : [];
  const categories = categoriesData instanceof Array ? categoriesData : [];

  const tag = useAppSelector((state) => state.tagSlice.tag);

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return <components.Header burgerIcon={true} basket={true} search={true} />;
  };

  const renderTags = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{paddingLeft: 20}}
        style={{flexGrow: 0, marginVertical: 20}}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        decelerationRate={0}
      >
        {tags.map((item, index, array) => {
          const lastElement = index === array.length - 1;
          return (
            <TouchableOpacity
              key={item.id}
              style={{
                paddingHorizontal: 18,
                paddingVertical: 6,
                borderWidth: 1,
                borderRadius: 3,
                marginRight: lastElement ? 20 : 10,
                borderColor:
                  item.name === tag
                    ? theme.colors.mainColor
                    : theme.colors.lightBlue,
                backgroundColor:
                  item.name === tag
                    ? theme.colors.mainColor
                    : 'rgba(219, 233, 245, 0.15)',
              }}
              onPress={() => {
                dispatch(setTag(item.name));
              }}
            >
              <Text
                style={{
                  color:
                    item.name === tag
                      ? theme.colors.white
                      : theme.colors.mainColor,
                  textTransform: 'uppercase',
                  ...theme.fonts.DMSans_700Bold,
                  lineHeight: 12 * 1.7,
                }}
              >
                {item.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const renderCategories = () => {
    const blockWidth = responsiveWidth(100) / 2 - 30;
    const blockHeight = responsiveWidth(100) / 2 - 30;

    const scrollViewStyle: object = {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingLeft: 20,
      flexDirection: 'row',
      justifyContent: 'space-between',
      flexWrap: 'wrap',
    };

    const touchableOpacityStyle: object = {
      width: blockWidth,
      height: blockHeight,
      marginBottom: 14,
      borderRadius: 5,
      backgroundColor: theme.colors.imageBackground,
    };

    const imageBackgroundStyle: object = {
      width: blockWidth,
      height: blockHeight,
      borderRadius: 10,
      paddingHorizontal: 14,
      paddingTop: 14,
      paddingBottom: 12,
      justifyContent: 'space-between',
    };

    const blockStyles: object = {
      borderWidth: 1,
      alignSelf: 'flex-start',
      borderColor: theme.colors.lightBlue,
      backgroundColor: theme.colors.white,
      borderRadius: 3,
      paddingVertical: 1,
      paddingHorizontal: 8,
    };

    // получаем массив с продуктами, у которых есть категории
    const notNull = products?.filter((e) => e.categories) ?? [];

    return (
      <ScrollView contentContainerStyle={{...scrollViewStyle}}>
        {categories.map((item, index, array) => {
          const data = notNull.filter((e) => e.categories.includes(item.name));
          const dataFilter = data.filter((e) => e.tags.includes(tag));
          const qty = dataFilter.length;
          return qty ? (
            <TouchableOpacity
              style={{...touchableOpacityStyle}}
              key={item.id}
              onPress={() => {
                navigation.navigate('Shop', {
                  title: item.name,
                  products: dataFilter,
                });
              }}
            >
              <ImageBackground
                source={{uri: item.image}}
                imageStyle={{borderRadius: 5}}
                style={{...imageBackgroundStyle}}
              >
                <View style={{...blockStyles}}>
                  <text.T14>{qty}</text.T14>
                </View>
                <text.T14 numberOfLines={1}>{item.name}</text.T14>
              </ImageBackground>
            </TouchableOpacity>
          ) : null;
        })}
      </ScrollView>
    );
  };

  const renderContent = (): JSX.Element => {
    if (productsLoading || categoriesLoading || tagsLoading) {
      return <components.Loader />;
    }

    return (
      <View style={{flex: 1}}>
        {renderTags()}
        {renderCategories()}
      </View>
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
      </components.TabBar>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderTabBar()}
    </components.SmartView>
  );
};

export default Categories;
