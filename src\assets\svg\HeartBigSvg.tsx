import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
import type {PropsWithChildren} from 'react';

type Props = PropsWithChildren<{
  strokeColor?: string;
  fillColor?: string;
}>;

const HeartBigSvg: React.FC<Props> = ({
  fillColor,
  strokeColor = '#4A5F73',
}): JSX.Element => {
  return (
    <Svg width={24} height={24} fill='none'>
      <Path
        fill={fillColor}
        stroke={strokeColor}
        strokeLinecap='round'
        strokeLinejoin='round'
        d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.501 5.501 0 1 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78v0Z'
      />
    </Svg>
  );
};

export default HeartBigSvg;
