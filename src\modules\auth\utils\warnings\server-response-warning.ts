export function getSignInStatusWarning(status: number) {
  if (status === 401) {
    return 'Incorrect email or password';
  } else if (status === 400) {
    return 'Invalid data provided';
  } else if (status === 404) {
    return 'User not found';
  } else if (status === 409) {
    return 'Account was created with Facebook or Google';
  } else {
    return 'An unexpected error occurred';
  }
}

export function getSignUpStatusWarning(status: number) {
  if (status === 400) {
    return {
      generalWarning: 'Email already exists',
      email: 'Email already exists',
    };
  } else {
    return {
      generalWarning: 'An unexpected error occurred',
    };
  }
}

export function getEmailSubmitionStatusWarning(status: number) {
  if (status === 400) {
    return 'Feature not available for Facebook or Google accounts';
  } else if (status === 404) {
    return 'User not found';
  } else {
    return 'An unexpected error occurred';
  }
}

export function getPasswordChangementWarning(status: number, code: string) {
  if (status === 400) {
    if (code === 'P2011') {
      return {
        generalWarning: 'Feature not available for Facebook or Google accounts',
        passwordWarning: '',
      };
    } else if (code === 'P4000') {
      return {
        generalWarning: 'Old password is incorrect',
      };
    }
  } else if (status === 500) {
    if (code === 'P1001') {
      return {
        generalWarning: 'Server error occurred',
      };
    }
  } else if (status === 404) {
    if (code === 'P2001') {
      return {
        generalWarning: 'User not found',
      };
    }
  } else {
    return {
      generalWarning: 'An unexpected error occurred',
      passwordWarning: '',
    };
  }
}

export function getEmailConfirmationWarning(status: number) {
  if (status === 400) {
    return 'Your token has expired';
  } else if (status === 404) {
    return 'Token is invalid';
  }

  return '';
}
