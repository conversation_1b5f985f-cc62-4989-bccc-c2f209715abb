import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet';
import CartIcon from '@assets/icons/cart';
import ProductContainer from '@/modules/cart/components/product-container';
import Text from '@/styles/text-styles';
import {useTranslations} from 'next-intl';
import {Button} from '@/components/ui/button';
import useCartVisibility from '../store/cart-visibility-store';
import {useCart} from '../store/cart-store';
import {useRouter, usePathname} from 'next/navigation';
import useScreenSize from '@/hooks/use-screen-size';

import SimilarProducts from './similar-products';
import {ShoppingBag} from 'lucide-react';
import EmptyCart from './empty-cart';
import {Separator} from '@/components/ui/separator';

export default function Cart() {
  const t = useTranslations('shared.cart');
  const {cartItems} = useCart();
  const {cartIsOpen, setCartIsOpen} = useCartVisibility();
  const router = useRouter();
  const pathname = usePathname();
  const {width} = useScreenSize();
  const isMobile = width < 768;

  return (
    <>
      <Sheet open={cartIsOpen} onOpenChange={(open) => setCartIsOpen(open)}>
        {!isMobile && (
          <SheetTrigger asChild className='pl-2 z-[10000]'>
            <div className='relative'>
              {cartItems.length > 0 && (
                <Text
                  textStyle='TS8'
                  className='absolute -right-2 -top-[6px] w-[20px] h-[20px] p-[2px] bg-blue rounded-full flex items-center justify-center text-white z-[10001]'
                >
                  {cartItems.length}
                </Text>
              )}

              <Button
                variant='ghost'
                size='icon'
                className={`relative rounded-full z-[10000] ${
                  cartIsOpen || pathname.startsWith('/paiement')
                    ? 'bg-blue text-white'
                    : 'bg-gray/10 text-blue'
                }`}
              >
                <ShoppingBag size={20} />
              </Button>
            </div>
          </SheetTrigger>
        )}
        <SheetContent className='p-0 overflow-hidden text-primary h-full max-w-sm L:max-w-lg flex flex-col justify-between z-[10000]'>
          <SheetHeader className='L:px-6 pt-6 px-4'>
            <SheetTitle className='flex items-center space-x-2 text-primary '>
              <CartIcon />
              <Text textStyle='TS5' className='font-bold text-gray-dark'>
                {t('title')}
              </Text>
            </SheetTitle>
          </SheetHeader>
          {cartItems.length > 0 ? (
            <>
              <div className='flex-1 overflow-y-auto pt-1 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-primary max-h-full'>
                <div className='flex flex-col space-y-4'>
                  {cartItems.map((item) => (
                    <div key={item.id} className='pr-4 L:px-6 px-4'>
                      <ProductContainer productItem={item} />
                    </div>
                  ))}
                </div>
              </div>

              <div className='sticky bottom-0 z-10 py-3 ps-4 L:px-6 px-2  bg-white'>
                <Button
                  className='w-full h-[56px] bg-primary hover:bg-primary/90 text-white rounded-full flex items-center justify-center'
                  onClick={() => {
                    router.push('/paiement');
                    setCartIsOpen(false);
                  }}
                >
                  <Text textStyle='TS5' className='text-white font-medium'>
                    {t('checkout')}
                  </Text>
                  <div className='bg-white rounded-full w-7 h-7 flex items-center justify-center'>
                    <Text textStyle='TS6' className='text-primary font-bold'>
                      {cartItems.length}
                    </Text>
                  </div>
                </Button>
              </div>

              <Separator />

              {/* Similar Products */}
              <SimilarProducts />
            </>
          ) : (
            <EmptyCart />
          )}
        </SheetContent>
      </Sheet>
    </>
  );
}
