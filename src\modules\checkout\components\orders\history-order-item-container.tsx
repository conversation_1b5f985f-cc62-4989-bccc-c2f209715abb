"use client";

import Image from "next/image";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { OrderItemType } from "../../types/orders";
import { useEffect, useState } from "react";

interface Props {
  item: OrderItemType;
}

export default function HistoryOrderItemContainer({ item }: Props) {
  const t = useTranslations("ordersManagement.order.labels");
  const { currency } = useCurrency();

  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  useEffect(() => {
    if (item && item.image) setProductImage(item.image);
  }, [item]);

  return (
    <div>
      <div className="flex gap-4">
        <Image
          src={productImage}
          onError={() => setProductImage("/not-found/product-image.webp")}
          alt={item.name}
          width={80}
          height={80}
          unoptimized
          className="h-20 w-20 rounded-lg overflow-hidden border border-primary"
        />

        <div className="flex-1 space-y-2">
          <div className="flex justify-between w-1/2">
            <Text textStyle="TS5" className="text-black font-bold">
              {item.name}
            </Text>
          </div>

          <div className="flex-1 flex items-end justify-between space-x-2">
            {item.price !== item.promotionnalPrice ? (
              <Text
                textStyle="TS4"
                className="text-center text-primary L:flex-row flex-col flex gap-2"
              >
                <span className="line-through">{`${item.price} ${currency}`}</span>
                <span className="">{`${item.promotionnalPrice} ${currency}`}</span>
              </Text>
            ) : (
              <Text textStyle="TS4" className="text-center text-primary">
                {`${item.price} ${currency}`}
              </Text>
            )}

            <div className="w-fit px-2 py-[6px] flex items-center gap-2 mt-2">
              <Text
                textStyle="TS7"
                className="text-center text-primary font-bold"
              >
                {`${t("quantity")}: ${item.quantity}`}
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
