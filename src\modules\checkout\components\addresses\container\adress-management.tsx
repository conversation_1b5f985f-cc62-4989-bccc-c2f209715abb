import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AddressType } from "@/modules/checkout/types/addresses";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

interface Props {
  address: AddressType;
  onDelete: (id: string) => void;
  bottomDelimter?: boolean;
}

export default function AddressManagementContainer({
  address,
  onDelete,
  bottomDelimter = true,
}: Props) {
  const t = useTranslations("accountPage.accountAdress.buttons");

  return (
    <div
      className={cn("py-3 border-gray border-opacity-50", {
        "border-b": bottomDelimter,
      })}
    >
      <Text textStyle="TS5" className="text-black font-bold text-xl">
        {`${address.firstName} ${address.lastName}`}
      </Text>
      <div className="flex justify-between">
        <Text textStyle="TS5" className="text-black  text-xl">
          {`${address.address1} ${address.city?.name} ${
            address.postalCode ? address.postalCode : ""
          }`}
        </Text>
        <Button variant={"ghost"} onClick={() => onDelete(address.id)}>
          <Text textStyle="TS6" className="text-danger-600 font-bold">
            {t("delete")}
          </Text>
        </Button>
      </div>
    </div>
  );
}
