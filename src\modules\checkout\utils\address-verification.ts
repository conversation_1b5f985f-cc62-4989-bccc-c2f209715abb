import {Country} from '@shopify/address';
import parsePhoneNumberFromString from 'libphonenumber-js';
import {City} from '../types/addresses';

export default function verifyAddressContent(
  formData: Record<string, string>,
  optionnalLabels: string[],
  country: Country,
  city: City,
) {
  const wrongInputsFinded: string[] = [];
  let warning = '';
  const neededData = [
    'email',
    'firstName',
    'lastName',
    'address1',
    'address2',
    'company',
    'province',
    'zip',
    'phone',
  ];
  const addressData: {[key: string]: string} = {};

  neededData.forEach((dataId) => {
    const value = formData[dataId]?.trim();

    if (value && value !== '') {
      if (dataId === 'zip') {
        addressData['postalCode'] = value;
      } else if (dataId === 'phone') {
        addressData['phone'] = `+${country?.phoneNumberPrefix}${value}`;
      } else {
        addressData[dataId] = value;
      }
    } else if (!optionnalLabels.includes(dataId)) {
      wrongInputsFinded.push(dataId);
    }
  });

  // Handle city input
  if (country?.code !== 'TN') {
    const cityValue = formData['city']?.trim();
    if (cityValue && cityValue !== '') {
      addressData['cityId'] = cityValue;
    } else {
      wrongInputsFinded.push('city');
    }
  } else {
    addressData['cityId'] = city.code;
  }

  // Phone number validation
  const phoneNumber = `+${country?.phoneNumberPrefix}${formData['phone']}`;
  if (!parsePhoneNumberFromString(phoneNumber)?.isValid()) {
    wrongInputsFinded.push('phone');
    warning = 'Please enter a valid phone number.';
  }

  return {
    addressData,
    valid: wrongInputsFinded.length === 0,
    wrongInputsFinded,
    warning:
      wrongInputsFinded.length > 0
        ? warning || 'Please fill in all the required fields.'
        : '',
  };
}
