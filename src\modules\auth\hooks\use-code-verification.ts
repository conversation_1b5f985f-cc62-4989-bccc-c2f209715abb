import useTimer from './use-timer';
import {verifyResetPasswordCode} from '../services/reset-password/code-verification';
import {useState} from 'react';

export default function useCodeVerification() {
  const [code, setCode] = useState('');
  const {displayedTimer, startTimer} = useTimer(600);

  async function verifyCode(
    email: string,
    code: string,
  ): Promise<{message: string; verified: boolean}> {
    try {
      const res = await verifyResetPasswordCode({email, code});
      return {
        message: res.ok ? '' : 'Invalid code',
        verified: res.ok,
      };
    } catch (error) {
      return {message: '', verified: false};
    }
  }

  return {
    verifyCode,
    displayedTimer,
    startTimer,
    code,
    setCode,
  };
}
