import {
  FlatList,
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
} from 'react-native';
import useProducts from '../../hooks/products/use-products';
import {ProductsSectionsVariant} from '../../types';
import {getCriteriaBasedOnProductsVariant} from '../../utils/criteria-based-on-variant';
import {ProductType} from '../../types/products';
import {components} from '../../../../components';

interface Props {
  type: ProductsSectionsVariant;
  similarProductSlug?: string;
}

export default function ProductsLists({type, similarProductSlug}: Props) {
  const {
    products,
    productsAreLoading,
    setPage,
    page,
    pagesNumber,
    paginatedListRef: scrollViewPortRef,
  } = useProducts({
    limit: 20,
    criteria: getCriteriaBasedOnProductsVariant(type),
    similarProductSlug,
  });

  const renderProduct = ({item, index}: {item: ProductType; index: number}) => {
    const isLastInRow = (index + 1) % 2 === 0;

    return (
      <View style={[styles.productItem, {marginRight: isLastInRow ? 0 : 10}]}>
        <components.ProductCard item={item} version={2} lastItem={false} />
      </View>
    );
  };

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>No Products Found</Text>
    </View>
  );

  if (productsAreLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color='#007AFF' />
        <Text style={styles.loadingText}>Loading products...</Text>
      </View>
    );
  }

  return (
    <ScrollView ref={scrollViewPortRef} style={styles.container}>
      <FlatList
        data={products || []}
        renderItem={({item, index}) => renderProduct({item, index})}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        numColumns={2}
        columnWrapperStyle={styles.row}
        ListEmptyComponent={renderEmptyComponent}
        contentContainerStyle={styles.listContainer}
        scrollEnabled={false}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  productItem: {
    flex: 0.48,
  },
  emptyContainer: {
    height: 450,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
});
