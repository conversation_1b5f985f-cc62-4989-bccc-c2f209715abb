import HomeTabSvg from '../assets/svg/tabs/HomeTabSvg';
import SearchTabSvg from '../assets/svg/tabs/SearchTabSvg';
import BasketTabSvg from '../assets/svg/tabs/BasketTabSvg';
import WishlistTabSvg from '../assets/svg/tabs/WishlistTabSvg';
import UserTabSvg from '../assets/svg/tabs/UserTabSvg';
import HomeSvg from '../assets/svg/HomeSvg';
import BriefcaseSvg from '../assets/svg/BriefcaseSvg';
import MapPinSvg from '../assets/svg/MapPinSvg';
import WarningSvg from '../assets/svg/WarningSvg';
import SuccessSvg from '../assets/svg/SuccessSvg';
import NotificationGiftSvg from '../assets/svg/NotificationGiftSvg';

const history = [
  {
    id: 1,
    number: 648752,
    date: 'Feb 25, 2023 at 8:32 PM',
    total: 281.85,
    status: 'Shipping',
    delivery: 15,
    discount: 29.65,
    products: [
      {
        id: 1,
        name: 'Small leather backpack, blue',
        quantity: 1,
        price: 167.5,
      },
      {
        id: 2,
        name: 'Shor summer dress, red, S',
        filling: 'vanilla',
        quantity: 1,
        price: 129.0,
      },
    ],
  },
  {
    id: 2,
    number: 648752,
    date: 'Feb 25, 2023 at 8:32 PM',
    total: 281.85,
    status: 'Delivered',
    delivery: 15,
    discount: 29.65,
    products: [
      {
        id: 1,
        name: 'Small leather backpack, blue',
        quantity: 1,
        price: 167.5,
      },
      {
        id: 2,
        name: 'Shor summer dress, red, S',
        filling: 'vanilla',
        quantity: 1,
        price: 129.0,
      },
    ],
  },
  {
    id: 1,
    number: 648752,
    date: 'Feb 25, 2023 at 8:32 PM',
    total: 281.85,
    status: 'Canceled',
    delivery: 15,
    discount: 29.65,
    products: [
      {
        id: 1,
        name: 'Small leather backpack, blue',
        quantity: 1,
        price: 167.5,
      },
      {
        id: 2,
        name: 'Shor summer dress, red, S',
        filling: 'vanilla',
        quantity: 1,
        price: 129.0,
      },
    ],
  },
];

const tabs = [
  {
    id: 1,
    name: 'Home',
    icon: HomeTabSvg,
  },
  {
    id: 2,
    name: 'Search',
    icon: SearchTabSvg,
  },
  {
    id: 3,
    name: 'Order',
    icon: BasketTabSvg,
  },
  {
    id: 4,
    name: 'Wishlist',
    icon: WishlistTabSvg,
  },
  {
    id: 5,
    name: 'Profile',
    icon: UserTabSvg,
  },
];

const onboardingData = [
  {
    id: 1,
    title: 'Welcome\nto Kastelli!',
    description: 'Labore sunt culpa excepteur\nculpa occaecat ex nisi mollit.',
    image: 'https://george-fx.github.io/kastelli/onboarding/01.jpg',
  },
  {
    id: 2,
    title: 'Easy Track\nyour Order!',
    description: 'Labore sunt culpa excepteur\nculpa occaecat ex nisi mollit.',
    image: 'https://george-fx.github.io/kastelli/onboarding/02.jpg',
  },
  {
    id: 3,
    title: 'Door to Door\nDelivery!',
    description: 'Labore sunt culpa excepteur\nculpa occaecat ex nisi mollit.',
    image: 'https://george-fx.github.io/kastelli/onboarding/03.jpg',
  },
];

const privacyPolicy = [
  {
    id: 1,
    title: '1. Terms',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 2,
    title: '2. Use license',
    description:
      'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 3,
    title: '3. Disclaimer',
    description:
      'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.  Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 4,
    title: '4. Limitations',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
  },
];

const support = [
  {
    id: 1,
    title: 'Refund Status: Common Questions',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 2,
    title: 'Troubleshooting Failed Payments',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 3,
    title: 'How to Find Your Missing Course',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 4,
    title: 'Downloading Course Resources',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 5,
    title: 'How to Refund a Course',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
  {
    id: 6,
    title: 'Lifetime Access',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  },
];

const coupons = [
  {
    id: 1,
    title: 'Mobile Application Development',
    discountInPercent: 10,
    activationCode: 'START',
  },
  {
    id: 2,
    title: 'UI/UX Design',
    discountInPercent: 30,
    activationCode: '4386',
    backgroundImage: 'https://george-fx.github.io/nuton/background/05.png',
  },
  {
    id: 3,
    title: 'Java from Scratch to Expert',
    discountInPercent: 25,
    activationCode: 'START',
  },
  {
    id: 4,
    title: 'Instagram Marketing 2022',
    discountInPercent: 40,
    activationCode: 'START',
  },
];

const reviews = [
  {
    id: 1,
    name: 'Adrianna Mercado',
    image: 'https://george-fx.github.io/kastelli/reviews/01.jpg',
    comment:
      'Consequat ut ea dolor aliqua laborum tempor Lorem culpa. Commodo veniam sint est mollit proident commodo.',
    rating: 5,
    date: '23 Mar',
  },
  {
    id: 2,
    name: 'Dante Valdez',
    image: 'https://george-fx.github.io/kastelli/reviews/02.jpg',
    comment:
      'Consequat ut ea dolor aliqua laborum tempor Lorem culpa. Commodo veniam sint est mollit proident commodo.',
    rating: 5,
    date: '23 Mar',
  },
  {
    id: 3,
    name: 'Troy Ingram',
    image: 'https://george-fx.github.io/kastelli/reviews/03.jpg',
    comment:
      'Consequat ut ea dolor aliqua laborum tempor Lorem culpa. Commodo veniam sint est mollit proident commodo.',
    rating: 5,
    date: '23 Mar',
  },
  {
    id: 4,
    name: 'Joshua Bean',
    image: 'https://george-fx.github.io/kastelli/reviews/04.jpg',
    comment:
      'Consequat ut ea dolor aliqua laborum tempor Lorem culpa. Commodo veniam sint est mollit proident commodo.',
    rating: 5,
    date: '23 Mar',
  },
];

const tags = [
  {
    id: 1,
    name: 'kids',
  },
  {
    id: 2,
    name: 'men',
  },
  {
    id: 3,
    name: 'women',
  },
  {
    id: 4,
    name: 'accessories',
  },
  {
    id: 5,
    name: 'shoes',
  },
  {
    id: 6,
    name: 'sports',
  },
];

const addresses = [
  {
    id: '1',
    type: 'Home',
    address: '8000 S Kirkland Ave, Chicago, IL 6065...',
    icon: HomeSvg,
  },
  {
    id: '2',
    type: 'Work',
    address: '8000 S Kirkland Ave, Chicago, IL 6066...',
    icon: BriefcaseSvg,
  },
  {
    id: '3',
    type: 'Other',
    address: '8000 S Kirkland Ave, Chicago, IL 6067...',
    icon: MapPinSvg,
  },
];

const sortingBy = [
  {id: 1, title: 'Best match'},
  {id: 2, title: 'Price: low to high'},
  {id: 3, title: 'Price: high to low'},
  {id: 4, title: 'Newest'},
  {id: 5, title: 'Customer rating'},
  {id: 6, title: 'Most popular'},
];

const promocodes = [
  {
    id: 1,
    name: '20lamplight',
    discount: '20',
    status: 'active',
    valid_till: 'Expire Dec 31, 2023',
  },
  {
    id: 2,
    name: '25%fridaysale',
    discount: '25',
    status: 'active',
    valid_till: 'Expire Dec 31, 2023',
  },
  {
    id: 3,
    name: '10%rooms23',
    discount: '10',
    status: 'expired',
    valid_till: 'Expire in 3 days',
  },
];

const sizes = [
  {
    id: 1,
    name: 'XS',
  },
  {
    id: 2,
    name: 'S',
  },
  {
    id: 3,
    name: 'M',
  },
  {
    id: 4,
    name: 'L',
  },
  {
    id: 5,
    name: 'XL',
  },
  {
    id: 6,
    name: 'XXL',
  },
];

const notifications = [
  {
    id: 1,
    icon: WarningSvg,
    title: 'Please confirm your email.',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    date: 'Feb 26, 2023 at 12:36 PM',
  },
  {
    id: 2,
    icon: SuccessSvg,
    title: 'Your support ticket №78912365',
    date: 'Feb 29, 2023 at 12:36 PM',
  },
  {
    id: 3,
    icon: NotificationGiftSvg,
    title: 'Black Friday Sales!',
    image: 'https://george-fx.github.io/kastelli/notification/01.jpg',
    description:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    date: 'Feb 29, 2023 at 12:36 PM',
  },
];

const payments = [
  {
    id: 1,
    type: 'Visa',
    number: ' **** 4864',
    icon: 'https://george-fx.github.io/kastelli/payments/01.png',
  },
  {
    id: 2,
    type: 'Mastercard',
    number: ' **** 3597',
    icon: 'https://george-fx.github.io/kastelli/payments/02.png',
  },
  {
    id: 3,
    type: 'Apple Pay',
    icon: 'https://george-fx.github.io/kastelli/payments/03.png',
  },
];

export {
  tabs,
  onboardingData,
  privacyPolicy,
  support,
  coupons,
  promocodes,
  reviews,
  sizes,
  tags,
  payments,
  history,
  addresses,
  sortingBy,
  notifications,
};
