import React, {useState} from 'react';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import {View, Text, ScrollView, TouchableOpacity} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

import {text} from '../text';
import {svg} from '../assets/svg';
import {components} from '../components';
import {theme, sizes} from '../constants';
import {useAppNavigation} from '../hooks';
import type {RootStackParamList} from '../types';

import {useGetTagsQuery, useGetColorsQuery} from '../store/slices/apiSlice';

type Props = NativeStackScreenProps<RootStackParamList, 'Filter'>;

const Filter: React.FC<Props> = (): JSX.Element => {
  const navigation = useAppNavigation();

  const {
    data: tagsData,
    error: tagsError,
    isLoading: tagsIsLoading,
  } = useGetTagsQuery();

  const {
    data: colorsData,
    error: colorsError,
    isLoading: colorsIsLoading,
  } = useGetColorsQuery();

  const tags = tagsData instanceof Array ? tagsData : [];
  const colors = colorsData instanceof Array ? colorsData : [];

  const [top, setTop] = useState(false);
  const [sale, setSale] = useState(false);
  const [newest, setNewest] = useState(false);

  const [rating1, setRating1] = useState(false);
  const [rating2, setRating2] = useState(false);
  const [rating3, setRating3] = useState(false);
  const [rating4, setRating4] = useState(false);
  const [rating5, setRating5] = useState(false);

  const [selectedSize, setSelectedSize] = useState<string>('M');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');

  if (tagsIsLoading || colorsIsLoading) {
    return <components.Loader />;
  }

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header goBack={true} title='Filter' />;
  };

  const renderStatus = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          marginBottom: 28,
        }}
      >
        <components.Status
          onPress={() => setSale(!sale)}
          title='sale'
          active={sale}
        />
        <components.Status
          onPress={() => setTop(!top)}
          title='top'
          active={top}
        />
        <components.Status
          onPress={() => setNewest(!newest)}
          title='new'
          active={newest}
        />
      </View>
    );
  };

  const renderMarker = (e: any) => {
    return (
      <View style={{alignItems: 'center'}}>
        <View
          style={{
            width: 20,
            height: 20,
            marginHorizontal: 10,
            backgroundColor: theme.colors.mainColor,
            borderRadius: 5,
            alignSelf: 'center',
          }}
        />
        <Text
          style={{
            position: 'absolute',
            bottom: -30,
            ...theme.fonts.textStyle_14,
            fontSize: 14,
            color: theme.colors.textColor,
            lineHeight: 16 * 1.6,
          }}
        >
          ${e.currentValue}
        </Text>
      </View>
    );
  };

  const renderPrice = () => {
    return (
      <View style={{marginBottom: 48}}>
        <Text
          style={{
            marginBottom: 28,
            ...theme.fonts.H5,
            color: theme.colors.mainColor,
          }}
        >
          Price
        </Text>
        <MultiSlider
          isMarkersSeparated={true}
          customMarkerLeft={(e) => renderMarker(e)}
          customMarkerRight={(e) => renderMarker(e)}
          values={[0, 800]}
          min={0}
          max={800}
          step={1}
          sliderLength={theme.sizes.width - 40}
          // onValuesChange={(e) => {}}
          selectedStyle={{
            backgroundColor: theme.colors.mainColor,
            width: 300,
          }}
          unselectedStyle={{
            backgroundColor: '#DBE3F5',
            width: 300,
          }}
          containerStyle={{
            height: 20,
            width: '100%',
          }}
          trackStyle={{
            height: 3,
            width: '100%',
          }}
        />
      </View>
    );
  };

  const renderRating = () => {
    return (
      <View>
        <Text
          style={{
            marginBottom: 20,
            ...theme.fonts.H5,
            color: theme.colors.mainColor,
          }}
        >
          Rating
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            flexWrap: 'wrap',
            marginBottom: 28,
          }}
        >
          <components.Rating
            title='1,0'
            onPress={() => setRating1(!rating1)}
            active={rating1}
            version={1}
          />
          <components.Rating
            title='2,0'
            onPress={() => setRating2(!rating2)}
            active={rating2}
            version={1}
          />
          <components.Rating
            title='3,0'
            onPress={() => setRating3(!rating3)}
            active={rating3}
            version={1}
          />
          <components.Rating
            title='4,0'
            onPress={() => setRating4(!rating4)}
            active={rating4}
            version={1}
          />
          <components.Rating
            title='5,0'
            onPress={() => setRating5(!rating5)}
            active={rating5}
            version={1}
          />
        </View>
      </View>
    );
  };

  const renderColor = () => {
    return (
      <View
        style={{
          marginRight: 20,
          marginBottom: 30,
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <text.H5 style={{marginRight: 20}}>Color</text.H5>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexWrap: 'wrap',
          }}
        >
          {colors.map((item, index, array) => {
            return (
              <TouchableOpacity
                key={index}
                style={{
                  width: 30,
                  height: 30,
                  borderRadius: 5,
                  marginRight: 14,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: item.hex,
                }}
                onPress={() => setSelectedColor(item)}
              >
                {selectedColor === item && <svg.ColorSelectSvg />}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSize = () => {
    return (
      <View
        style={{
          marginBottom: 28,
        }}
      >
        <text.H5
          style={{
            marginBottom: 20,
          }}
        >
          Size
        </text.H5>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexWrap: 'wrap',
          }}
        >
          {sizes.map((item, index, array) => {
            return (
              <TouchableOpacity
                key={item.id}
                style={{
                  paddingHorizontal: 12,
                  borderWidth: 1,
                  paddingVertical: 10,
                  borderRadius: 3,
                  marginBottom: 10,
                  marginRight: 10,
                  borderColor:
                    selectedSize === item.name
                      ? theme.colors.mainColor
                      : theme.colors.lightBlue,
                  backgroundColor:
                    selectedSize === item.name
                      ? theme.colors.mainColor
                      : '#FAFCFE',
                }}
                onPress={() => setSelectedSize(item.name)}
              >
                <Text
                  style={{
                    color:
                      selectedSize === item.name
                        ? theme.colors.white
                        : theme.colors.mainColor,
                    ...theme.fonts.DMSans_700Bold,
                    fontSize: 12,
                    lineHeight: 12 * 1.7,
                  }}
                >
                  {item.name}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderTags = (): JSX.Element | null => {
    if (tags.length === 0) {
      return null;
    }
    return (
      <View>
        <Text
          style={{
            marginBottom: 20,
            ...theme.fonts.H5,
            color: theme.colors.mainColor,
          }}
        >
          Tags
        </Text>
        <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
          {tags.map((item, index, array) => {
            return (
              <TouchableOpacity
                key={item.id}
                style={{
                  paddingHorizontal: 12,
                  borderWidth: 1,
                  paddingVertical: 10,
                  borderRadius: 3,
                  marginBottom: 10,
                  marginRight: 10,
                  borderColor:
                    selectedTag === item.name
                      ? theme.colors.mainColor
                      : theme.colors.lightBlue,
                  backgroundColor:
                    selectedTag === item.name
                      ? theme.colors.mainColor
                      : '#FAFCFE',
                }}
                onPress={() => setSelectedTag(item.name)}
              >
                <Text
                  style={{
                    color:
                      selectedTag === item.name
                        ? theme.colors.white
                        : theme.colors.mainColor,
                    ...theme.fonts.DMSans_700Bold,
                    fontSize: 12,
                    lineHeight: 12 * 1.7,
                  }}
                >
                  {item.name}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    return (
      <components.Button
        title='apply filters'
        onPress={() => {
          navigation.goBack();
        }}
        containerStyle={{
          padding: 20,
        }}
      />
    );
  };

  const renderContent = () => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingTop: 20,
          paddingHorizontal: 20,
        }}
      >
        {renderStatus()}
        {renderPrice()}
        {renderRating()}
        {renderColor()}
        {renderSize()}
        {renderTags()}
      </ScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderFooter()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default Filter;
