"use client";
import { useState } from "react";
import FilterChoiceContainer from "./filter-choice-container";
import { GroupedVariationType } from "../../../types/products";
import Text from "@/styles/text-styles";
import useVariations from "../../../hooks/products/use-variations";

export default function VariationsSelectionDropDown() {
  const { variations, variationsAreLoading } = useVariations();

  if (variationsAreLoading || variations.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {variations.map((variation) => (
        <VariationGroup key={variation.name} variation={variation} />
      ))}
    </div>
  );
}

function VariationGroup({ variation }: { variation: GroupedVariationType }) {
  return (
    <FilterChoiceContainer title={variation.name}>
      <div className="space-y-3">
        {variation.values.map((value, idx) => (
          <VariationSelection
            key={idx}
            variationName={variation.name}
            value={value}
          />
        ))}
      </div>
    </FilterChoiceContainer>
  );
}

function VariationSelection({
  variationName,
  value,
}: {
  variationName: string;
  value: string;
}) {
  const [checked, setChecked] = useState<boolean>(false);

  return (
    <div className="flex items-center space-x-3 w-fit cursor-pointer hover:bg-gray-50 rounded-md py-1 transition-colors px-2">
      <div
        className={`w-4 h-4 rounded-md flex items-center justify-center p-0.5 bg-gray-light `}
      >
        <div
          className={`w-full h-full rounded-sm flex items-center justify-center  ${
            checked && "bg-blue "
          }`}
        ></div>
      </div>

      <Text
        textStyle="TS6"
        className={`text-gray-dark font-bold group-hover:font-bold ${
          checked && "text-blue"
        }`}
      >
        {value}
      </Text>
    </div>
  );
}
