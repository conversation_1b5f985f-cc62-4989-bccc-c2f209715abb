import {useQuery} from '@tanstack/react-query';
import getLandingPageContent from '../services/landing-page';
import {LandingPageContent} from '../types/landing-page';

export default function useLandingPage() {
  const {data, isLoading, isError} = useQuery<LandingPageContent | null>({
    queryKey: ['landing-page'],
    queryFn: getLandingPageContent,
  });

  return {
    isLoading,
    landingPageContent: data,
  };
}
