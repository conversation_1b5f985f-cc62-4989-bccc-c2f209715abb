import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';

import {text} from '../../../text';
import {theme} from '../../../constants';
import {ProductType} from '../../../modules/catalog/types/products';
import {components} from '../../../components';
import {
  useGetTagsQuery,
  useGetBannersQuery,
  useGetProductsQuery,
  useGetCarouselQuery,
} from '../../../store/slices/apiSlice';
import {useAppNavigation, useAppDispatch} from '../../../hooks';

// backend data
import useCategories from '../../../modules/catalog/hooks/categories/use-categories';
import useLandingPage from '../../../hooks/use-landing-page';
import useProducts from '../../../modules/catalog/hooks/products/use-products';
import useBrands from '../../../modules/catalog/hooks/brands/use-brands';

const _v1: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();

  // Original template data (keep for fallback)
  const {
    data: productsData,
    error: productsError,
    isLoading: productsLoading,
  } = useGetProductsQuery();

  const {
    data: carouselData,
    error: carouselError,
    isLoading: carouselLoading,
  } = useGetCarouselQuery();

  const {
    data: tagsData,
    error: tagsError,
    isLoading: tagsLoading,
  } = useGetTagsQuery();

  const {
    data: bannersData,
    error: bannersError,
    isLoading: bannersLoading,
  } = useGetBannersQuery();

  // Real backend data
  const {categories, categoriesAreLoading} = useCategories();
  const {brands, brandsAreLoading} = useBrands({limit: 20});
  const {landingPageContent, isLoading: landingPageLoading} = useLandingPage();

  const {products: bestSellerProducts, productsAreLoading: bestSellersLoading} =
    useProducts({
      limit: 8,
      criteria: 'mostSold',
    });

  const {products: featuredProducts, productsAreLoading: featuredLoading} =
    useProducts({
      limit: 8,
      criteria: 'createdAtDesc',
    });

  const {products: newProducts, productsAreLoading: newProductsLoading} =
    useProducts({
      limit: 8,
      criteria: 'createdAtDesc',
    });

  if (
    productsLoading ||
    carouselLoading ||
    tagsLoading ||
    bannersLoading ||
    categoriesAreLoading ||
    brandsAreLoading ||
    bestSellersLoading ||
    featuredLoading ||
    landingPageLoading
  ) {
    return <components.Loader />;
  }

  const banners = bannersData instanceof Array ? bannersData : [];
  const products = productsData instanceof Array ? productsData : [];
  const carousel = carouselData instanceof Array ? carouselData : [];

  // Use catalog products directly
  const catalogBestSellers = bestSellerProducts || [];
  const catalogFeatured = featuredProducts || [];
  const catalogNewProducts = newProducts || [];

  const renderHeroImages = () => {
    const heroImages = landingPageContent?.images || [];

    if (heroImages.length === 0) {
      // Fallback to old carousel if no hero images
      const sale = catalogBestSellers?.filter((e: ProductType) => {
        const firstItem = e.items?.[0];
        const firstPrice = firstItem?.prices?.[0];
        return firstPrice && firstPrice.promotionalPrice < firstPrice.realPrice;
      });
      return (
        <ScrollView
          horizontal={true}
          pagingEnabled={true}
          showsHorizontalScrollIndicator={false}
          bounces={false}
          alwaysBounceHorizontal={false}
          style={{flexGrow: 0}}
        >
          {carousel?.map((item, index, array) => {
            return (
              <components.CarouselItem
                item={item}
                key={item.id}
                array={array}
                index={index}
                sale={sale}
              />
            );
          })}
        </ScrollView>
      );
    }

    return (
      <ScrollView
        horizontal={true}
        pagingEnabled={true}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        alwaysBounceHorizontal={false}
        style={{flexGrow: 0, marginBottom: 50}}
      >
        {heroImages.map((heroImage, index) => {
          return (
            <TouchableOpacity
              key={index}
              onPress={() => {
                // Handle hero image navigation if needed
                if (heroImage.link) {
                  // Could navigate to a specific screen or open URL
                  console.log('Hero image clicked:', heroImage.link);
                }
              }}
            >
              <ImageBackground
                source={{uri: heroImage.mobileImage}}
                style={{
                  width: theme.sizes.width,
                  aspectRatio: 1.775,
                  backgroundColor: theme.colors.imageBackground,
                }}
                imageStyle={{
                  borderBottomRightRadius: 5,
                  borderTopRightRadius: 5,
                }}
                resizeMode='cover'
              />
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const renderTags = (): JSX.Element => {
    const displayCategories = categories?.slice(0, 8) || [];

    return (
      <ScrollView
        horizontal={true}
        decelerationRate={0}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingLeft: 20, marginBottom: 50}}
      >
        {displayCategories?.map((item, index, array) => {
          const qty = Math.floor(Math.random() * 20) + 5;
          return (
            <TouchableOpacity
              key={item.id}
              onPress={() => {
                navigation.navigate('CatalogProducts');
              }}
            >
              <ImageBackground
                source={{uri: item.image || ''}}
                style={{
                  width: 90,
                  height: 90,
                  padding: 10,
                  justifyContent: 'space-between',
                  marginRight: index === array.length - 1 ? 20 : 14,
                }}
                imageStyle={{
                  borderRadius: 5,
                }}
                resizeMode='cover'
              >
                <View
                  style={{
                    borderWidth: 1,
                    alignSelf: 'flex-start',
                    borderColor: theme.colors.lightBlue,
                    backgroundColor: theme.colors.white,
                    borderRadius: 3,
                    paddingHorizontal: 3,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 8,
                      textTransform: 'uppercase',
                      lineHeight: 8 * 1.5,
                      color: theme.colors.textColor,
                      ...theme.fonts.DMSans_400Regular,
                    }}
                  >
                    {qty}
                  </Text>
                </View>
                <Text
                  style={{
                    ...theme.fonts.DMSans_400Regular,
                    color: theme.colors.mainColor,
                    fontSize: 10,
                    lineHeight: 10 * 1.5,
                    textTransform: 'capitalize',
                  }}
                >
                  {item.name}
                </Text>
              </ImageBackground>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const renderBestSellers = (): JSX.Element | null => {
    const slice = catalogBestSellers?.slice(0, 4) || [];

    if (slice.length > 0) {
      return (
        <View style={{marginBottom: 50}}>
          <components.BlockHeading
            title='Best Sellers'
            containerStyle={{paddingHorizontal: 20}}
            onPress={() => {
              navigation.navigate('CatalogProducts');
            }}
          />
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{paddingLeft: 20}}
            decelerationRate={0}
          >
            {slice?.map(
              (item: ProductType, index: number, array: ProductType[]) => {
                const lastItem = index === array.length - 1;
                return (
                  <components.ProductCard
                    key={index}
                    item={item}
                    version={1}
                    lastItem={lastItem}
                  />
                );
              },
            )}
          </ScrollView>
        </View>
      );
    }

    return null;
  };

  const renderBanner = (): JSX.Element => {
    const banner = banners[1];
    return (
      <TouchableOpacity
        style={{
          marginRight: 20,
          marginBottom: 50,
        }}
        onPress={() => {
          navigation.navigate('Shop', {
            title: 'Sale',
            products: products.filter((e) => e.old_price),
          });
        }}
      >
        <ImageBackground
          source={{uri: banner?.image}}
          style={{
            width: '100%',
            aspectRatio: 1.775,
            backgroundColor: theme.colors.imageBackground,
          }}
          imageStyle={{
            borderBottomRightRadius: 5,
            borderTopRightRadius: 5,
          }}
          resizeMode='cover'
        >
          <View style={{paddingHorizontal: 20, paddingTop: 30}}>
            <text.H2>{banner?.title_line_1}</text.H2>
            <text.H2 style={{marginBottom: 20}}>{banner?.title_line_2}</text.H2>
            <components.ShopNow
              // onPress={() => navigation.navigate('Shop', {category: 'all'})}
              text={banner?.button_text}
            />
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  };

  const renderFeatured = (): JSX.Element | null => {
    // Use real backend featured products
    const slice = catalogFeatured?.slice(0, 5) || [];
    return (
      <View style={{marginBottom: 50}}>
        <components.BlockHeading
          title='Featured Products'
          containerStyle={{paddingHorizontal: 20}}
          onPress={() => {
            navigation.navigate('CatalogProducts');
          }}
        />
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{paddingLeft: 20}}
          decelerationRate={0}
        >
          {slice?.map(
            (item: ProductType, index: number, array: ProductType[]) => {
              const lastItem = index === array.length - 1;
              return (
                <components.ProductCard
                  key={index}
                  item={item}
                  version={2}
                  lastItem={lastItem}
                />
              );
            },
          )}
        </ScrollView>
      </View>
    );
  };

  const renderNewProducts = (): JSX.Element | null => {
    const slice = catalogNewProducts?.slice(0, 4) || [];

    if (slice.length > 0) {
      return (
        <View style={{marginBottom: 50}}>
          <components.BlockHeading
            title='New Arrivals'
            containerStyle={{paddingHorizontal: 20}}
            onPress={() => {
              navigation.navigate('CatalogProducts');
            }}
          />
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{paddingLeft: 20}}
            decelerationRate={0}
          >
            {slice?.map(
              (item: ProductType, index: number, array: ProductType[]) => {
                const lastItem = index === array.length - 1;
                return (
                  <components.ProductCard
                    key={index}
                    item={item}
                    version={1}
                    lastItem={lastItem}
                  />
                );
              },
            )}
          </ScrollView>
        </View>
      );
    }

    return null;
  };

  const renderCatalogProducts = (): JSX.Element => {
    return (
      <View style={{marginBottom: 50}}>
        <components.BlockHeading
          title='Browse Full Catalog'
          containerStyle={{paddingHorizontal: 20}}
          onPress={() => {
            navigation.navigate('CatalogProducts');
          }}
        />
        <View style={{paddingHorizontal: 20}}>
          <TouchableOpacity
            style={{
              backgroundColor: theme.colors.mainColor,
              borderRadius: 10,
              padding: 20,
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: 100,
            }}
            onPress={() => {
              navigation.navigate('CatalogProducts');
            }}
          >
            <Text
              style={{
                color: theme.colors.white,
                fontSize: 18,
                fontWeight: 'bold',
                marginBottom: 8,
              }}
            >
              Explore All Products
            </Text>
            <Text
              style={{
                color: theme.colors.white,
                fontSize: 14,
                textAlign: 'center',
                opacity: 0.9,
              }}
            >
              Advanced filtering • Real-time search • Categories & Brands
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <React.Fragment>
      {renderHeroImages()}
      {renderTags()}
      {renderNewProducts()}
      {renderBestSellers()}
      {renderBanner()}
      {renderFeatured()}
      {renderCatalogProducts()}
    </React.Fragment>
  );
};

export default _v1;
