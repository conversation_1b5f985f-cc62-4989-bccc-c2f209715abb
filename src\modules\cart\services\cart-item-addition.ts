import {AxiosError} from 'axios';
import {PostedCartItemType} from '../types/products';
import extractJWTokens from '../../auth/utils/jwt/extract-tokens';
import {POST} from '../../../lib/http-methods';
import {refreshToken} from '../../auth/services/refresh-token';
import {CustomError} from '../../../lib/custom-error';

export async function addCartItemOnServerSide(item: PostedCartItemType) {
  const {access} = await extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await POST('/carts/products/register', header, {
      productItemId: item.id,
      quantity: item.quantity,
    });
    return {ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() => addCartItemOnServerSide(item));
      if (!res) throw new CustomError('Unauthorized', 401);
    } else if (axiosError.response?.status == 404) {
      throw new CustomError('Product Not Found!', 404);
    } else throw new CustomError('Server Error!', 500);
  }
}
