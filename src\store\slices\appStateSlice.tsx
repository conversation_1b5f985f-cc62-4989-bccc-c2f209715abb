import {createSlice, PayloadAction} from '@reduxjs/toolkit';

type AppStateType = {
  isFirstTime: boolean;
  refreshToken: string | null;
  accessToken: string | null;
  homeVersion: number;
  productVersion: number;
  orderHistoryVersion: number;
};

const initialState: AppStateType = {
  isFirstTime: true,
  refreshToken: null,
  accessToken: null,
  homeVersion: 2,
  productVersion: 2,
  orderHistoryVersion: 1,
};

const appStateSlice = createSlice({
  name: 'appState',
  initialState,
  reducers: {
    setIsFirstTime: (state, action: PayloadAction<boolean>) => {
      state.isFirstTime = action.payload;
    },
    setRefreshToken: (state, action: PayloadAction<string | null>) => {
      state.refreshToken = action.payload;
    },
    setAccessToken: (state, action: PayloadAction<string | null>) => {
      state.accessToken = action.payload;
    },
    setHomeVersion: (state, action: PayloadAction<number>) => {
      state.homeVersion = action.payload;
    },
    setProductVersion: (state, action: PayloadAction<number>) => {
      state.productVersion = action.payload;
    },
    setOrderHistoryVersion: (state, action: PayloadAction<number>) => {
      state.orderHistoryVersion = action.payload;
    },
  },
});

export const {
  setIsFirstTime,
  setRefreshToken,
  setAccessToken,
  setHomeVersion,
  setProductVersion,
  setOrderHistoryVersion,
} = appStateSlice.actions;

export {appStateSlice};
