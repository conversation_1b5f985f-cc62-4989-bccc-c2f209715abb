import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { useResetPasswordContext } from "@auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function CodeStep() {
  const t = useTranslations("shared.reset-password");

  const { warning, submitCode, displayedTimer, code, setCode, isLoading } =
    useResetPasswordContext();

  return (
    <div className={"flex flex-col space-y-1"}>
      <Text textStyle="TS4" className="font-bold text-start text-primary">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS7" className="text-gray">
        <span>{t.raw("codeStep.description")}</span>
        <span className="text-white">{`(${displayedTimer})`}</span>
      </Text>
      <div className={"flex space-x-2 w-fit"}></div>

      <Text textStyle="TS7" className="w-full text-danger">
        {warning.generalWarning}
      </Text>
      <div className="pt-6 flex flex-col space-y-6">
        <InputOTP
          maxLength={5}
          value={code}
          onChange={(value: string) => setCode(value)}
          pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
        >
          <InputOTPGroup className="w-full rounded-md border border-primary py-2 flex overflow-hidden text-primary">
            {Array.from({ length: 5 }).map((_, idx) => (
              <div className="w-1/6 flex justify-center items-center" key={idx}>
                <InputOTPSlot
                  className={cn(
                    "w-fit min-w-4 px-1 py-1 border-b text-primary ",
                    TextStyle["TS6"]
                  )}
                  index={idx}
                />
              </div>
            ))}
          </InputOTPGroup>
        </InputOTP>
        <Button
          className={cn(
            "w-full h-[56px] bg-primary text-white rounded-lg text-base font-semibold",
            { "opacity-70": isLoading }
          )}
          onClick={() => submitCode(code)}
          disabled={code.length < 5 || isLoading}
        >
          <Text textStyle="TS7">{t.raw("codeStep.button")}</Text>
        </Button>

        {/* <div className="w-full flex justify-center">
          <Button variant="ghost" onClick={() => setStep("email")}>
            <Text textStyle="TS7" className="font-tajawal text-gray underline">
              {t.raw("return")}
            </Text>
          </Button>
        </div> */}
        {/* Bottom redirect text */}
        <div className="w-full text-center space-y-3 pt-4">
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.haveAccount", {
                loginLink: (chunk) => (
                  <Link
                    href="/auth/sign-in"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS7" className="text-gray-600">
              {t.rich("authLinks.noAccount", {
                signupLink: (chunk) => (
                  <Link
                    href="/auth/sign-up"
                    className="text-primary hover:underline font-medium"
                  >
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
