"use client";
import AccountMenu from "./account-menu";
import useScreenSize from "@/hooks/use-screen-size";
import { cn } from "@/lib/utils";
import { notFound, usePathname } from "next/navigation";
import Link from "next/link";
import ChevronLeft from "@assets/icons/chevrons/left";
import { useTranslations } from "next-intl";
import UserIcon from "@assets/icons/user/user";
import OrdersIcon from "@assets/icons/user/orders";
import MapPin from "@assets/icons/map-pin";
import SettingsIcon from "@assets/icons/settings";
import Text from "@/styles/text-styles";
import useUserStore from "../../store/user-store";

interface AccountPageProps {
  children: React.ReactNode;
}

export default function AccountPage({ children }: AccountPageProps) {
  const { width } = useScreenSize();
  const { user, isLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage");
  const menuContent = useTranslations("accountPage.menuList");
  const pathname = usePathname();
  const menuButtons = [
    {
      icon: <UserIcon />,
      pathname: "/mon-compte/info",
      name: "myAccount",
    },
    {
      icon: <OrdersIcon />,
      pathname: "/mon-compte/commandes",
      name: "ordersHistory",
    },
    {
      icon: <MapPin />,
      pathname: "/mon-compte/adresses",
      name: "adress",
    },
    {
      icon: <SettingsIcon />,
      pathname: "/mon-compte/parametres",
      name: "settings",
    },
  ];
  const choosedButton = menuButtons.find((btn) =>
    pathname.endsWith(btn.pathname)
  );

  if (!isLoading && !user?.isAuthenticated) notFound();

  return (
    <div className="extraL:px-8 L:pt-[10px] flex flex-col space-y-3 mt-12">
      <div className="flex items-center space-x-4 mb-8">
        {choosedButton && (
          <Link
            href={width < 850 ? "/mon-compte" : "/"}
            className="flex items-center gap-2 text-primary font-semibold"
          >
            <ChevronLeft width={10} height={10} color="#254F84" />
            <Text textStyle="TS5">
              {width < 850 ? menuContent(choosedButton.name) : t("home")}
            </Text>
          </Link>
        )}
      </div>

      <div
        className={cn(
          "relative flex extraL:flex-row flex-col extraL:space-x-10 extraL:space-y-0 space-y-10 space-y-reverse "
        )}
      >
        {(width < 850 && pathname.endsWith("mon-compte")) || width >= 850 ? (
          <div
            className={cn("extraL:flex-none flex-1", {
              "order-2": pathname.endsWith("mon-compte") && width < 850,
            })}
          >
            <AccountMenu menuButtons={menuButtons} />
          </div>
        ) : null}
        <div
          className={cn(
            "flex-1 XL:px-5 extraL:px-2 px-4 extraL:pl-[30px] XL:pl-[60px]",
            {
              "order-1": pathname.endsWith("mon-compte") && width < 850,
            }
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
}
