import React from 'react';
import {ScrollView} from 'react-native';

import {theme, reviews} from '../constants';
import {components} from '../components';
import type {RootStackParamList} from '../types';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'Reviews'>;

const Reviews: React.FC<Props> = (): JSX.Element => {
  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = () => {
    return (
      <components.Header goBack={true} title='Reviews' bottomLine={true} />
    );
  };

  const renderContent = () => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingLeft: 20,
          paddingTop: 25,
          paddingBottom: 20,
        }}
      >
        {reviews.map((item, index, array) => {
          return (
            <components.ReviewItem
              item={item}
              key={index}
              array={array}
              index={index}
            />
          );
        })}
      </ScrollView>
    );
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default Reviews;
