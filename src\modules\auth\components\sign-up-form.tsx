"use client";
import AuthInput from "./auth-input";
import Text from "@/styles/text-styles";
import { useState } from "react";
import useAuth from "@auth/hooks/use-auth";
import { useTranslations } from "next-intl";
import ExternalAuth from "./external-auth";
import Link from "next/link";

export default function SignUpForm() {
  const t = useTranslations("shared.auth");
  const [email, setEmail] = useState("");
  const { warning, submitInfo, isLoading, authRef } = useAuth(
    "signUp",
    setEmail
  );

  return (
    <div className="flex flex-col ">
      <div className="w-full flex flex-col items-start space-y-2">
        <div>
          <Text textStyle="TS2" className="font-bold text-primary text-2xl">
            {t("signUp.title")}
          </Text>
        </div>

        <AuthInput
          formRef={authRef}
          warning={warning}
          submitInfo={submitInfo}
          authType="signUp"
          isLoading={isLoading}
        />

        <ExternalAuth title={t("continueWith")} />

        {/* Bottom redirect text */}
        <div className="w-full text-center pb-4 block">
          <Text textStyle="TS7">
            {t.rich("signUp.description", {
              underline: (chunk) => (
                <Link
                  href="/auth/sign-in"
                  className="hover:underline cursor-pointer text-primary font-medium"
                >
                  {chunk}
                </Link>
              ),
            })}
          </Text>
        </div>
      </div>
    </div>
  );
}
