import {castToPriceRange} from '../../utils/types-casting/products';
import {PriceRange, PriceRangeInResponse} from '../../types/products';
import {GET} from '../../../../lib/http-methods';

export async function retrieveMinMaxPriceFromServerSide(
  currency?: string,
): Promise<PriceRange | null> {
  try {
    const currencyParam = currency ? `currency=${currency}` : '';
    const res = await GET(`/products/price-range?${currencyParam}`, {});

    return castToPriceRange(res.data as PriceRangeInResponse);
  } catch (error) {
    return null;
  }
}
