import { OrderDataType, OrderResponseDataType } from "../../types/orders";

export function castToOrderType(order: OrderResponseDataType): OrderDataType {
  const orderDiscount = calculateOrderDiscounts(order);

  return {
    id: order.id,
    code: order.code,
    total: Number(order.total) + orderDiscount,
    shippingCost: Number(order.shippingCost),
    currency: order.currency,
    status: order.status,
    createdAt: order.createdAt,
    notes: order.notes,
    voucherCode: order.voucherCode,
    address: {
      email: order.address.email,
      firstName: order.address.firstName,
      lastName: order.address.lastName,
      address1: order.address.address1,
      address2: order.address.address2 || null,
      company: order.address.company || null,
      zone: order.address.zone || null,
      postalCode: order.address.postalCode || null,
      phone: order.address.phone || "",
      city: order.address.city
        ? {
            name: order.address.city.name,
            country: {
              name: order.address.city.country.name,
              isoCode: order.address.city.country.isoCode,
            },
          }
        : {
            name: "",
            country: { name: "", isoCode: "" },
          },
    },
    discount: orderDiscount,
    items: order.items.map((orderItem) => {
      return {
        couponCode: orderItem.voucherCode,
        image: orderItem.image
          ? `${process.env.BACKEND_ADDRESS}${orderItem.image}`
          : "/not-found/product-image.webp",
        name: orderItem.name,
        quantity: orderItem.quantity,
        barcode: orderItem.barcode,
        price: Number(orderItem.price),
        promotionnalPrice: Number(orderItem.price) - getItemDiscount(orderItem),
        promotion: orderItem.promotion
          ? {
              name: orderItem.promotion.name,
              discount: {
                type: orderItem.promotion.discount.type,
                value: Number(orderItem.promotion.discount.value),
              },
            }
          : null,
      };
    }),
  };
}

function calculateOrderDiscounts(order: OrderResponseDataType): number {
  let totalDiscount = 0;

  order.items.forEach((item) => {
    totalDiscount += getItemDiscount(item) * Number(item.quantity);
  });

  return totalDiscount;
}

function getItemDiscount(item: OrderResponseDataType["items"][number]): number {
  let itemDiscount = 0;

  if (item.promotion && item.promotion.discount) {
    const promotionDiscount = Number(item.promotion.discount.value);

    if (item.promotion.discount.type === "percentage") {
      itemDiscount += promotionDiscount * Number(item.price);
    } else {
      itemDiscount += promotionDiscount;
    }
  }

  if (item.voucherCode && item.voucherCode.discount) {
    const voucherDiscount = Number(item.voucherCode.discount.value);
    if (item.voucherCode.discount.type === "percentage") {
      itemDiscount += voucherDiscount * Number(item.price);
    } else {
      itemDiscount += voucherDiscount;
    }
  }

  return itemDiscount;
}
