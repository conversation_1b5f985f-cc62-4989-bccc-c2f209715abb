import {useToast} from '@/hooks/use-toast';
import {useTranslations} from 'next-intl';
import {useRouter, useSearchParams} from 'next/navigation';
import {useEffect} from 'react';

export default function useExternalAuth() {
  const t = useTranslations('shared.auth.warnings.externalAuth');

  const router = useRouter();
  const searchParams = useSearchParams();
  const externalAuthResult = searchParams.get('auth');
  const externalAuthStatus = searchParams.get('status');

  const {toast} = useToast();

  useEffect(() => {
    setTimeout(() => {
      if (externalAuthResult === 'failed') {
        toast({
          title: 'Error :',
          description:
            externalAuthStatus == '409'
              ? 'This email address is already linked to an existing account.'
              : 'An issue occurred while signing in or creating your account.',
        });
        router.replace('/');
      }
    }, 0);
  }, []);
}
