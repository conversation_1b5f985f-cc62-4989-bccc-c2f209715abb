import { cn } from "@/lib/utils";
import TimeDownCounter from "@/components/time-down-counter";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import Text, { TextStyle } from "@/styles/text-styles";
import { Dayjs } from "dayjs";
import { useTranslations } from "next-intl";

interface Props {
  code: string;
  forever: boolean;
  date: Dayjs;
  discount: { type: "percentage" | "amount"; value: number };
}

export default function UserCouponCodeDetails({
  code,
  date,
  forever,
  discount,
}: Props) {
  const t = useTranslations("shared.coupon");
  const { currency } = useCurrency();

  return (
    <div className="flex justify-between items-center text-primary mt-5">
      <Text textStyle="TS6">{code}</Text>
      <Text textStyle="TS6">
        {discount.type == "percentage"
          ? `${discount.value} %`
          : `${discount.value} ${currency}`}
      </Text>
      {date && forever == true ? (
        <Text textStyle="TS6">{t("forever")}</Text>
      ) : (
        <TimeDownCounter
          className={cn("underline", TextStyle["TS6"])}
          targetDate={date}
        />
      )}
    </div>
  );
}
